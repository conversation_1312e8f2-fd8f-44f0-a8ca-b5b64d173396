import axios from 'axios';

const PIPEDRIVE_API_KEY = process.env.NEXT_PUBLIC_PIPEDRIVE_API_KEY || '';

export const createNewDeal = async (data: any) => {
  try {
    const result = await axios({
      url: encodeURI(`https://api.pipedrive.com/v1/deals?api_token=${PIPEDRIVE_API_KEY}`),
      method: 'POST',
      data,
    });
    return result.data;
  } catch (error) {
    console.error(error);
    return null;
  }
};

export const createNewPerson = async (data: any) => {
  try {
    const result = await axios({
      url: encodeURI(`https://api.pipedrive.com/v1/persons?api_token=${PIPEDRIVE_API_KEY}`),
      method: 'POST',
      data,
    });
    return result.data;
  } catch (error) {
    console.error(error);
    return null;
  }
};

export const searchPerson = async (data: any) => {
  try {
    const result = await axios({
      url: encodeURI(
        `https://api.pipedrive.com/v1/persons/search?term=${data.email}&fields=email&api_token=${PIPEDRIVE_API_KEY}`,
      ),
      method: 'GET',
      data,
    });
    return result.data;
  } catch (error) {
    console.error(error);
    return null;
  }
};

export const getPersonId = async (data: any) => {
  const {
    data: { items },
  } = await searchPerson(data);

  if (items?.[0]?.item?.id) {
    return items[0].item.id;
  } else {
    const name = `${data?.firstname} ${data?.lastname}`;
    const personData = {
      name: name,
      email: [
        {
          value: data?.email ?? '',
          primary: 'true',
          label: 'main',
        },
      ],
      phone: [
        {
          value: data?.phone ?? '',
          primary: 'true',
          label: 'mobile',
        },
      ],
    };
    const { data: person } = await createNewPerson(personData);
    return person.id;
  }
};
