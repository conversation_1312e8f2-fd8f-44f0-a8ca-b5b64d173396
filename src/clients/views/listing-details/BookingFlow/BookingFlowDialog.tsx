'use client';

import { useMemo } from 'react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogTitle,
} from '@/components/ui/dialog';
import { BookingFlowStep, useBooking } from '@/contexts/BookingContext';
import { PropertyDetails } from '@/types/properties';

import BillingDetailsStep from './BillingDetailsStep';
import RegisterStep from './RegisterStep';
import TripDetailsStep from './TripDetailsStep';

type Props = {
  propertyDetails: PropertyDetails;
};

const BookingFlowDialog = ({ propertyDetails }: Props) => {
  const { step } = useBooking();

  const currentScreen = useMemo(() => {
    switch (step) {
      case BookingFlowStep.DETAILS:
        return <TripDetailsStep data={propertyDetails} />;
      case BookingFlowStep.BILLING_DETAILS:
        return <BillingDetailsStep />;
      case BookingFlowStep.REGISTER:
        return <RegisterStep />;
    }
  }, [propertyDetails, step]);
  return (
    <Dialog open>
      <DialogOverlay className="bg-[rgba(217,217,217,0.90)]" />
      <DialogContent
        className="p-0 w-screen h-screen md:h-auto md:w-[380px]"
        hideCloseButton
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogTitle className="hidden" />
        <DialogDescription className="hidden" />
        <div className="px-4 py-5">
          {cu}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BookingFlowDialog;
