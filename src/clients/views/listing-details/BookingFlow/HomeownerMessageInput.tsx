'use client';

import { memo, useCallback } from 'react';

import { Textarea } from '@/components/ui/textarea';
import { useBooking } from '@/contexts/BookingContext';

const HomeownerMessageInput = memo(() => {
  const { message, setMessage, errors } = useBooking();

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { value } = event.target;

      setMessage(value);
    },
    [setMessage],
  );

  return (
    <>
      <Textarea
        name="message"
        value={message}
        placeholder="Example: “<PERSON>, myself, my partner and our two children ages 5 and 7 are coming to Nantucket for a family vacation and love the house!”"
        className="w-full rounded-[10px] !text-xs"
        rows={3}
        onChange={onChangeTextInput}
        error={!!errors['message'] && message.length === 0}
        helperText={errors['message'] && message.length === 0 ? errors['message'] : ''}
      />
    </>
  );
});

export default HomeownerMessageInput;
