'use client';

import { useCallback, useState } from 'react';

import Button from '@/clients/ui/Button';
import Checkbox from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { EMAIL_PATTERN } from '@/constants/patterns';
import useForm from '@/hooks/useForm';
import { Nullable, ProgressStatus } from '@/types/common';
import InputLabel from '@/ui/atoms/InputLabel';

import classNames from 'classnames';

export type RegisterFormValues = {
  firstname: string;
  lastname: string;
  phone: string;
  userLoginEmail: string;
  streetaddress1: string;
  userDateofBirth: Nullable<Date>;
};

type Props = {};

const RegisterStep = () => {
  const [selected, setSelected] = useState<Date | undefined>();
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(null);
  const [emailError, setEmailError] = useState<Nullable<string>>(null);
  const {
    formState,
    pristine,
    errors,
    onChange,
    preSubmitCheck: preSubmitCheck,
  } = useForm<RegisterFormValues>(
    {
      firstname: '',
      lastname: '',
      phone: '',
      userLoginEmail: '',
      streetaddress1: '',
      userDateofBirth: null,
    },
    {
      firstname: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter a first name`;
        }
      },
      lastname: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter a last name`;
        }
      },
      userLoginEmail: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter an email`;
        }

        if (!_value.match(EMAIL_PATTERN)) {
          return 'Invalid email address';
        }
      },
    },
    false,
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange],
  );

  // const onRegister = useCallback(() => {
  //   const _errors = preSubmitCheck();
  //   if (Object.values(_errors).some((_error) => _error !== '')) {
  //     return;
  //   }
  //   setProgressStatus(ProgressStatus.LOADING);
  //   actionSubmitRegisterData({
  //     ...formState,
  //     loginPassword: aRandomString(),
  //     nrrenter: true,
  //     phone_number: phone,
  //     company: '',
  //     propertymanager: false,
  //     propertyowner: false,
  //   })
  //     .then((data) => {
  //       console.debug('the data is', data);
  //       setAuthStep(AuthenticationStep.VERIFY_EMAIL_MESSAGE);
  //       setProgressStatus(ProgressStatus.SUCCESSFUL);
  //     })
  //     .catch((err) => {
  //       console.log('error is', err);
  //       if (err?.userLoginEmail) {
  //         setEmailError('User  with this email already exists!');
  //       } else {
  //         toast(err);
  //       }
  //       setProgressStatus(ProgressStatus.FAILED);
  //     });
  // }, [formState, phone, preSubmitCheck, selected, setAuthStep]);

  return (
    <>
      <p className="text-2xl font-semibold m-0 tracking-[-0.6px]">Complete your reservation</p>
      <Separator className="my-2" />
      <p className="text-sm m-0 mb-4">Step 2 of 3</p>
      <div className="mb-4">
        <InputLabel
          className={classNames(
            'text-[#18181B] text-sm mb-2',
            (!!errors?.firstname?.length || !!errors?.lastname?.length) && 'text-error',
          )}
        >
          Guest Name
        </InputLabel>
        <div className="flex items-center gap-x-3">
          <Input
            type="text"
            name="firstname"
            value={formState.firstname}
            placeholder="First name"
            className="w-full px-2.5 py-3 rounded-lg"
            onChange={onChangeTextInput}
            helperText={errors?.firstname ?? ''}
            error={!!errors?.firstname?.length}
          />
          <Input
            type="text"
            name="lastname"
            value={formState.lastname}
            placeholder="Last name"
            className="w-full px-2.5 py-3 rounded-lg"
            onChange={onChangeTextInput}
            helperText={errors?.lastname ?? ''}
            error={!!errors?.lastname?.length}
          />
        </div>
      </div>
      <div className="mb-4">
        <InputLabel
          className={classNames(
            'text-[#18181B] text-sm mb-2',
            !!errors?.phone?.length && 'text-error',
          )}
        >
          Phone number
        </InputLabel>
        <Input
          type="phone"
          name="phone"
          value={formState.phone}
          placeholder="Phone Number"
          className="w-full px-2.5 py-3 rounded-lg"
          pattern="\d*"
          inputMode="numeric"
          onChange={onChangeTextInput}
          helperText={errors?.phone ?? ''}
          error={!!errors?.phone?.length}
        />
        <p className="text-xs text-[#71717A] mt-2">
          We&apos;ll use this to send important trip updates.
        </p>
      </div>
      <div className="">
        <InputLabel
          className={classNames(
            'text-[#18181B] text-sm mb-2',
            !!errors?.userLoginEmail?.length && 'text-error',
          )}
        >
          Email address
        </InputLabel>
        <Input
          type="email"
          name="userLoginEmail"
          value={formState.userLoginEmail}
          placeholder="Enter your email address"
          className="w-full px-2.5 py-3 rounded-lg"
          onChange={onChangeTextInput}
          helperText={errors?.userLoginEmail ?? ''}
          error={!!errors?.userLoginEmail?.length}
        />
        <p className="text-xs text-[#71717A] mt-2">
          We&apos;ll email you trip confirmation receipts.
        </p>
      </div>
      <div className="flex items-center space-x-1.5 mt-2">
        <Checkbox id="travelInsurance" className="w-4 h-4" />
        <label htmlFor="ach" className="cursor-pointer text-xs text-[#71717A]">
          Please send me news and marketing information
        </label>
      </div>
      <Separator className="my-4" />
      <Button
        className="font-medium w-full rounded-full px-4 py-3"
        // onClick={onRegister}
        isLoading={progressStatus === ProgressStatus.LOADING}
      >
        Continue
      </Button>
    </>
  );
};

export default RegisterStep;
