'use client';

import { memo, useCallback } from 'react';

import { useBooking } from '@/contexts/BookingContext';
import { BookingPaymentMethod } from '@/types/booking';
import FormHelperText from '@/ui/atoms/FormHelperText';

const PaymentMethodSelect = memo(() => {
  const { paymentMethod, setPaymentMethod, errors } = useBooking();

  const onChangeMethod = useCallback(
    (method: BookingPaymentMethod) => {
      setPaymentMethod(method);
    },
    [setPaymentMethod],
  );

  return (
    <>
      <div className="flex items-center gap-x-3 mt-3">
        <div
          onClick={() => onChangeMethod(BookingPaymentMethod.ACH)}
          className="flex items-center w-full space-x-2 px-4 py-3 border border-solid border-[#E2E8F0] rounded-xl shadow-sm cursor-pointer"
        >
          <input
            id="ach"
            type="radio"
            className="w-5 h-5 m-0"
            onChange={() => onChangeMethod(BookingPaymentMethod.ACH)}
            checked={paymentMethod === BookingPaymentMethod.ACH}
          />
          <label htmlFor="ach" className="cursor-pointer text-sm m-0">
            ACH
          </label>
        </div>
        <div
          onClick={() => onChangeMethod(BookingPaymentMethod.CREDIT_CARD)}
          className="flex items-start w-full space-x-2 px-4 py-3 border border-solid border-[#E2E8F0] rounded-xl shadow-sm cursor-pointer"
        >
          <input
            id="ach"
            type="radio"
            className="w-5 h-5 m-0"
            onChange={() => onChangeMethod(BookingPaymentMethod.CREDIT_CARD)}
            checked={paymentMethod === BookingPaymentMethod.CREDIT_CARD}
          />
          <label htmlFor="ach" className="cursor-pointer text-sm m-0">
            Credit Card
          </label>
        </div>
      </div>
      <p className="text-xs text-right m-0 text-[#52525B] leading-[20px]">
        Credit card processing fees apply.
      </p>
      {errors['paymentMethod'] && !paymentMethod && (
        <FormHelperText error className="pl-4">
          {errors['paymentMethod']}
        </FormHelperText>
      )}
    </>
  );
});

export default PaymentMethodSelect;
