'use client';

import { useCallback, useState } from 'react';

import { ArrowRightIcon } from '@heroicons/react/24/outline';

import Button from '@/clients/ui/Button';
import Checkbox from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { useBooking } from '@/contexts/BookingContext';
import useForm from '@/hooks/useForm';
import { ProgressStatus } from '@/types/common';
import InputLabel from '@/ui/atoms/InputLabel';
import { currencyFormatter } from '@/utils/common';

import classNames from 'classnames';

import HomeownerMessageInput from './HomeownerMessageInput';
import PaymentMethodSelect from './PaymentMethodSelect';
import PriceDetails from './PriceDetails';

type BillingDetailsFormValues = {
  streetaddress1: string;
  city: string;
  state: string;
  message: string;
};

type Props = {};

const BillingDetailsStep = () => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(null);
  const { bookingAvailabilityData } = useBooking();
  const {
    formState,
    pristine,
    errors,
    onChange,
    preSubmitCheck: preSubmitCheck,
  } = useForm<BillingDetailsFormValues>(
    {
      streetaddress1: '',
      city: '',
      state: '',
      message: '',
    },
    {
      streetaddress1: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter a first name`;
        }
      },
    },
    false,
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange],
  );

  return (
    <>
      <p className="text-2xl font-semibold m-0 tracking-[-0.6px]">Billing Details</p>
      <Separator className="my-2" />
      <p className="text-sm m-0 mb-4">Step 2 of 3</p>
      <div className="h-[65dvh] overflow-y-scroll pb-2">
        <InputLabel className={classNames('text-[#18181B] text-sm mb-2')}>Address</InputLabel>
        <Input
          type="text"
          name="streetaddress1"
          value={formState.streetaddress1}
          placeholder="Street address"
          className="w-full px-2.5 py-3 rounded-lg"
          onChange={onChangeTextInput}
          helperText={errors?.streetaddress1 ?? ''}
          error={!!errors?.streetaddress1?.length}
        />
        <div className="flex gap-x-2 mt-3 w-full">
          <Input
            type="text"
            name="city"
            value={formState.city}
            placeholder="City"
            wrapperclassName="w-full"
            className="px-2.5 py-3 rounded-lg"
            onChange={onChangeTextInput}
            helperText={errors?.city ?? ''}
            error={!!errors?.city?.length}
          />
          <Input
            type="text"
            name="state"
            value={formState.state}
            placeholder="State"
            wrapperclassName="w-full"
            className="px-2.5 py-3 rounded-lg"
            onChange={onChangeTextInput}
            helperText={errors?.state ?? ''}
            error={!!errors?.state?.length}
          />
        </div>
        <Separator className="my-4" />
        <InputLabel className="text-sm font-medium text-[#18181B]">Payment Method</InputLabel>
        <PaymentMethodSelect />
        <div className="flex items-center w-full space-x-2 px-4 py-3 border border-solid border-[#E2E8F0] rounded-xl shadow-sm my-4">
          <Checkbox className="w-4 h-4" />
          <label htmlFor="ach" className="cursor-pointer text-xs md:text-sm">
            Yes, add Travel Protection Plan for{' '}
            {currencyFormatter.format(bookingAvailabilityData?.travel_insurance_amount ?? 0)}
          </label>
        </div>
        <div className="px-4 py-3 border border-solid border-[#E2E8F0] rounded-xl shadow-sm">
          <PriceDetails />
        </div>
        <div className="bg-[#ECFDF5] rounded-full px-2.5 py-1 mt-4 flex items-center gap-x-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
          >
            <path
              d="M2.56667 5.74667C2.3689 4.85583 2.64038 3.92572 3.28631 3.28115C3.93224 2.63657 4.86291 2.36704 5.75333 2.56667C6.24338 1.80025 7.09031 1.33652 8 1.33652C8.90969 1.33652 9.75662 1.80025 10.2467 2.56667C11.1385 2.36616 12.0709 2.63641 12.7172 3.28277C13.3636 3.92913 13.6338 4.86151 13.4333 5.75333C14.1997 6.24338 14.6635 7.09031 14.6635 8C14.6635 8.90969 14.1997 9.75662 13.4333 10.2467C13.633 11.1371 13.3634 12.0678 12.7189 12.7137C12.0743 13.3596 11.1442 13.6311 10.2533 13.4333C9.76389 14.2027 8.91522 14.6687 8.00333 14.6687C7.09144 14.6687 6.24278 14.2027 5.75333 13.4333C4.86291 13.633 3.93224 13.3634 3.28631 12.7189C2.64038 12.0743 2.3689 11.1442 2.56667 10.2533C1.79425 9.7645 1.32604 8.9141 1.32604 8C1.32604 7.0859 1.79425 6.2355 2.56667 5.74667"
              stroke="#047857"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M6 8L7.33333 9.33333L10 6.66667"
              stroke="#047857"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <p className="text-xs text-[#047857] font-medium m-0">
            Free cancellation before confirmation
          </p>
        </div>
        <div className="px-4 py-3 border border-solid border-[#E2E8F0] rounded-xl shadow-sm my-4">
          <div className="text-sm font-medium flex items-center justify-between">
            <span>Deposit due today</span>
            <span>
              {currencyFormatter.format(
                Number(bookingAvailabilityData?.payment_schedule?.[0][1] ?? 0),
              )}
            </span>
          </div>
          <p className="text-xs m-0 text-[#52525B]">
            This booking request is subject to availability. We will confirm availability of the
            home within 24 hours. If the home is unavailable, no payment will be taken, and
            we&apos;ll be in touch to help you find a great alternative.
          </p>
        </div>
        <p className="text-sm font-medium mt-4 mb-0 leading-[20px]">Include a Message</p>
        <HomeownerMessageInput />
      </div>
      <Separator className="my-4" />
      <Button className="w-full rounded-full px-5 py-3 font-medium">
        <ArrowRightIcon className="w-5 h-5 mr-2" />
        Submit Request
      </Button>
      <p className="text-xs text-[#52525B] m-0 leading-[20px]">
        By selecting Agree and continue, I agree to{' '}
        <a
          href="https://nantucketrentals.com/info/copyright"
          target="_blank"
          className="text-inherit"
        >
          Terms of Service
        </a>{' '}
        and acknowledge the{' '}
        <a
          href="https://nantucketrentals.com/info/privacy-policy"
          target="_blank"
          className="text-inherit"
        >
          Privacy Policy
        </a>
        .
      </p>
    </>
  );
};

export default BillingDetailsStep;
