'use client';

import { memo, useCallback, useEffect, useState } from 'react';

import { ChevronDownIcon } from '@heroicons/react/24/outline';

import LoadingSpinner from '@/components/ui/loading-spinner';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useBooking } from '@/contexts/BookingContext';
import { PropertyDetails } from '@/types/properties';
import InputLabel from '@/ui/atoms/InputLabel';

import { format } from 'date-fns';
import dynamic from 'next/dynamic';

const CheckoutDateRangePicker = dynamic(() => import('./CheckoutDateRangePicker/index'), {
  loading: () => (
    <div className="md:min-w-[570px] md:min-h-[468px] flex items-center justify-center">
      <LoadingSpinner />
    </div>
  ),
});

type Props = {
  propertyDetails: PropertyDetails;
  propertyId: number;
};

const CheckinCheckout = ({ propertyDetails, propertyId }: Props) => {
  const { date, setDate, bookingAvailabilityData } = useBooking();
  const [open, setOpen] = useState<boolean>(false);

  const onClear = useCallback(() => {
    setDate(undefined);
  }, [setDate]);

  const onClose = useCallback(() => {
    setOpen(false);
  }, []);

  const onClickedTrigger = useCallback((e: any) => {
    e.preventDefault();
  }, []);

  const onClick = useCallback(() => {
    setOpen(true);
  }, []);

  useEffect(() => {
    if (date?.from && date?.to) {
      onClose();
    }
  }, [date?.from, date?.to, onClose]);

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild onClick={onClickedTrigger}>
          <div className="flex items-center gap-x-2">
            <div className="w-1/2">
              <InputLabel className="px-[14px]">CHECK IN</InputLabel>
              <div
                onClick={onClick}
                onKeyDown={onClick}
                role="button"
                tabIndex={0}
                className="border border-solid border-platinium flex items-center gap-x-2 py-2.5 px-3.5 text-sm  text-metal-gray rounded-[40px] relative cursor-pointer"
              >
                {date?.from ? format(date?.from, 'LLL d, yyyy') : 'Add Date'}
                <ChevronDownIcon className="w-4 h-4 text-foundation-blue absolute right-4" />
              </div>
            </div>
            <div className="w-1/2">
              <InputLabel className="px-[14px]">CHECK OUT</InputLabel>
              <div
                onClick={onClick}
                onKeyDown={onClick}
                role="button"
                tabIndex={0}
                className="border border-solid border-platinium flex items-center gap-x-2 py-2.5 px-3.5 text-sm  text-metal-gray rounded-[40px] relative cursor-pointer"
              >
                {date?.to ? format(date?.to, 'LLL d, yyyy') : 'Add Date'}
                <ChevronDownIcon className="w-4 h-4 text-foundation-blue absolute right-4" />
              </div>
            </div>
          </div>
        </PopoverTrigger>

        {open && (
          <PopoverContent
            className="md:min-w-[554px] w-max border border-solid border-english-manor border-opacity-40 p-6"
            align="end"
          >
            <CheckoutDateRangePicker
              date={date}
              setDate={setDate}
              onClear={onClear}
              onClose={onClose}
              rentalRates={propertyDetails.rentalRates}
              availableCalendar={propertyDetails.availableCalendar}
              bookingAvailabilityData={bookingAvailabilityData}
              propertyId={propertyId}
            />
          </PopoverContent>
        )}
      </Popover>
    </>
  );
};

export default memo(CheckinCheckout);
