'use client';

import { useCallback, useMemo, useState } from 'react';

import { Cross2Icon } from '@radix-ui/react-icons';
import { DateRange } from 'react-day-picker';
import toast from 'react-hot-toast';

import { GuestsValues } from '@/clients/components/common/GuestSelector';
import Button from '@/clients/ui/Button';
import { Dialog, DialogContent, DialogDescription, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { EMAIL_PATTERN, PHONE_NUMBER_PATTERN } from '@/constants/patterns';
import useForm from '@/hooks/useForm';
import { createNewDeal, getPersonId } from '@/services/pipedrive';
import { PropertyDetails } from '@/types/properties';
import FormHelperText from '@/ui/atoms/FormHelperText';
import InputLabel from '@/ui/atoms/InputLabel';
import { identifyUserWithTriats, KlaviyoEvents, pushKlaviyoData } from '@/utils/klaviyoAnalytics';

import classNames from 'classnames';
import { differenceInCalendarDays, format } from 'date-fns';
import { usePathname, useSearchParams } from 'next/navigation';

import DateRangePickerInput from './DateRangePickerInput';
import GuestSelectorInput from './GuestSelectorInput';

type Props = {
  listingDetails: PropertyDetails;
  onToggle: () => void;
};

export type AskQuestionFormValues = {
  firstname: string;
  lastname: string;
  email: string;
  phone: string;
  message: string;
};

enum PIPEDRIVE_DATA_FIELDS_KEY {
  ARRIVAL_DATE = '****************************************',
  DEPARTURE_DATE = 'f9ef9427a5e3d4824781884a3218700c3d262a5d',
  CHILDREN_COUNT = '48b241ed5f7a524c3535bb7ecf730871dceb1f2e',
  ADULT_COUNT = '46438a0af3ab3e18c3782299502ac7b2c38ef90c',
  LISTING_ID = '2f4874e6c39a1c5c26150176686276fd608ec827',
  MESSAGE_TO_AGENT = '89713c48e5b7db9597efcff1bf223106a66f251a',
  LEASE_ID = 'c2de631d71eb17a31a7e7c78a3072f46c02237d9',
  LISTING_OWNER = '6f798e8e02b56430e756a4bbcc12c049088779b8',
}

const AskQuestionDialog = ({ listingDetails, onToggle }: Props) => {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [guestsValues, setGuestsValues] = useState<GuestsValues>({
    adults: 1,
    children: 0,
  });
  const [date, setDate] = useState<DateRange | undefined>(undefined);
  const {
    formState,
    errors,
    onChange,
    preSubmitCheck: preSubmitCheck,
  } = useForm<AskQuestionFormValues>(
    {
      firstname: '',
      lastname: '',
      email: '',
      phone: '',
      message: '',
    },
    {
      firstname: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter a first name`;
        }
      },
      lastname: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter a last name`;
        }
      },
      email: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter an email`;
        }

        if (!_value.match(EMAIL_PATTERN)) {
          return 'Invalid email address';
        }
      },
      phone: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter phone number`;
        }

        if (!_value.match(PHONE_NUMBER_PATTERN)) {
          return 'Invalid phone number';
        }
      },
      message: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter message`;
        }
      },
    },
    false,
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange],
  );

  const dateRangePickerError = useMemo(
    () => (!date?.from || !date?.to ? 'Please select dates' : null),
    [date?.from, date?.to],
  );

  const onSubmit = useCallback(async () => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== '') || !!dateRangePickerError) {
      return;
    }

    if (!date?.from || !date?.to) {
      return;
    }

    setSubmitting(true);
    const name = `${formState?.firstname} ${formState?.lastname}`;
    const asPath = `${pathname}${
      searchParams?.toString() ? `? ${searchParams?.toString() ?? ''}` : ''
    }`;
    const person_id = await getPersonId(formState);

    const pipeDriveData = {
      title: `${name} deal`,
      [PIPEDRIVE_DATA_FIELDS_KEY.ARRIVAL_DATE]: format(date?.from, 'yyyy-MM-dd'),
      [PIPEDRIVE_DATA_FIELDS_KEY.DEPARTURE_DATE]: format(date?.to, 'yyyy-MM-dd'),
      [PIPEDRIVE_DATA_FIELDS_KEY.CHILDREN_COUNT]: guestsValues.children ?? 0,
      [PIPEDRIVE_DATA_FIELDS_KEY.ADULT_COUNT]: guestsValues.adults ?? 1,
      [PIPEDRIVE_DATA_FIELDS_KEY.LISTING_ID]: listingDetails.nrPropertyId,
      [PIPEDRIVE_DATA_FIELDS_KEY.MESSAGE_TO_AGENT]: formState?.message ?? '',
      [PIPEDRIVE_DATA_FIELDS_KEY.LISTING_OWNER]: listingDetails?.nrPmoId,
      person_id,
      pipeline_id: 5,
    };

    identifyUserWithTriats(
      formState?.email ?? '',
      {
        email: formState?.email ?? '',
        firstName: formState?.firstname ?? '',
        lastName: formState?.lastname ?? '',
        phone: formState?.phone ?? '',
      },
      {
        url: asPath,
        path: pathname,
      },
    );

    createNewDeal(pipeDriveData)
      .then((dealData) => {
        setTimeout(() => {
          setSubmitting(false);
          toast.success('Ask a Question form submitted');
          pushKlaviyoData(KlaviyoEvents.LISTING_DETAILS_PAGE_QUESTION, {
            name,
            email: formState?.email ?? '',
            phone: formState?.phone ?? '',
            person_id,
            dates: [
              date?.from && format(date?.from, 'yyyy-MM-dd'),
              date?.to && format(date?.to, 'yyyy-MM-dd'),
            ],
            children: guestsValues?.children,
            adults: guestsValues?.adults,
            message: formState?.message ?? '',
            nrPropertyId: listingDetails.nrPropertyId,
            pmoId: listingDetails?.nrPmoId,
          });
          onToggle();
        }, 3000);
      })
      .catch((e) => console.log('error', e));
  }, [
    date?.from,
    date?.to,
    dateRangePickerError,
    formState,
    guestsValues.adults,
    guestsValues.children,
    listingDetails?.nrPmoId,
    listingDetails.nrPropertyId,
    onToggle,
    pathname,
    preSubmitCheck,
    searchParams,
  ]);

  return (
    <Dialog open onOpenChange={onToggle}>
      <DialogContent
        className="p-0 w-screen h-screen md:h-auto md:min-w-[220px] md:max-w-[600px]"
        hideCloseButton
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogTitle className="hidden" />
        <DialogDescription className="hidden" />
        <div className="w-full">
          <div className="px-[30px] py-4 relative font-medium text-xl leading-[140%] tracking-[0.5px]">
            Ask a Question
            <Button className="absolute right-[30px] !p-0" intent="ghost" onClick={onToggle}>
              <Cross2Icon className="h-5 w-5" />
            </Button>
          </div>
          <Separator />
          <div className="w-full p-[30px]">
            <div className="grid grid-cols-2 gap-x-4 mb-4">
              <div className="">
                <InputLabel className={classNames(!!errors?.firstname?.length && 'text-error')}>
                  First Name*
                </InputLabel>
                <Input
                  type="text"
                  name="firstname"
                  value={formState.firstname}
                  placeholder="Enter your first name"
                  className="w-full"
                  onChange={onChangeTextInput}
                  helperText={errors?.firstname ?? ''}
                  error={!!errors?.firstname?.length}
                />
              </div>
              <div className="">
                <InputLabel className={classNames(!!errors?.lastname?.length && 'text-error')}>
                  Last Name*
                </InputLabel>
                <Input
                  type="text"
                  name="lastname"
                  value={formState.lastname}
                  placeholder="Enter your last name"
                  className="w-full"
                  onChange={onChangeTextInput}
                  helperText={errors?.lastname ?? ''}
                  error={!!errors?.lastname?.length}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-x-4 mb-4">
              <div className="">
                <InputLabel className={classNames(!!errors?.email?.length && 'text-error')}>
                  Email*
                </InputLabel>
                <Input
                  type="text"
                  name="email"
                  value={formState.email}
                  placeholder="Enter your email"
                  className="w-full"
                  onChange={onChangeTextInput}
                  helperText={errors?.email ?? ''}
                  error={!!errors?.email?.length}
                />
              </div>
              <div className="">
                <InputLabel className={classNames(!!errors?.phone?.length && 'text-error')}>
                  Phone*
                </InputLabel>
                <Input
                  type="text"
                  name="phone"
                  value={formState.phone}
                  placeholder="Enter your phone number"
                  className="w-full"
                  onChange={onChangeTextInput}
                  helperText={errors?.phone ?? ''}
                  error={!!errors?.phone?.length}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-x-4 mb-4">
              <div className="">
                <InputLabel className={classNames(!!dateRangePickerError && 'text-error')}>
                  Travel Dates*
                </InputLabel>
                <DateRangePickerInput
                  date={date}
                  setDate={setDate}
                  propertyDetails={listingDetails}
                />
                {dateRangePickerError && (
                  <div className="ml-2">
                    <FormHelperText error>{dateRangePickerError}</FormHelperText>
                  </div>
                )}
              </div>
              <div className="">
                <InputLabel>Guests</InputLabel>
                <GuestSelectorInput guestsValues={guestsValues} setGuestsValues={setGuestsValues} />
              </div>
            </div>

            <div className="">
              <InputLabel className={classNames(!!errors?.message?.length && 'text-error')}>
                Message*
              </InputLabel>
              <Textarea
                name="message"
                value={formState.message}
                placeholder="Enter your message"
                className="w-full"
                onChange={onChangeTextInput}
                helperText={errors?.message ?? ''}
                error={!!errors?.message?.length}
              />
            </div>

            <div className="flex items-center justify-between mt-6">
              <Button
                intent="ghost"
                className="font-medium text-carolina-blue hover:text-carolina-blue "
                onClick={onToggle}
              >
                Close
              </Button>
              <Button
                className="font-medium w-full md:w-max min-w-[130px]"
                onClick={onSubmit}
                disabled={submitting}
                isLoading={submitting}
              >
                Submit
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AskQuestionDialog;
