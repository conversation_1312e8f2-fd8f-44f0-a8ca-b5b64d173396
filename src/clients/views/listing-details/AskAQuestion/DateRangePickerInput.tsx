'use client';

import { ComponentProps, useCallback, useEffect, useState } from 'react';

import { ChevronDownIcon } from '@heroicons/react/24/outline';
import { DateRange } from 'react-day-picker';

import LoadingSpinner from '@/components/ui/loading-spinner';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { PropertyDetails } from '@/types/properties';

import { format } from 'date-fns';
import dynamic from 'next/dynamic';

const CheckoutDateRangePicker = dynamic(() => import('../CheckoutDateRangePicker/index'), {
  loading: () => (
    <div className="md:min-w-[570px] md:min-h-[468px] flex items-center justify-center">
      <LoadingSpinner />
    </div>
  ),
});

type Props = {
  date?: DateRange;
  setDate: (_d?: DateRange) => void;
  propertyDetails: PropertyDetails;
} & ComponentProps<typeof PopoverContent>;

const DateRangePickerInput = ({ date, setDate, propertyDetails, ...rest }: Props) => {
  const [open, setOpen] = useState<boolean>(false);

  const onClear = useCallback(() => {
    setDate(undefined);
  }, [setDate]);

  const onClose = useCallback(() => {
    setOpen(false);
  }, []);

  const onClickedTrigger = useCallback((e: any) => {
    e.preventDefault();
  }, []);

  const onClick = useCallback(() => {
    setOpen(true);
  }, []);

  useEffect(() => {
    if (date?.from && date?.to) {
      onClose();
    }
  }, [date?.from, date?.to, onClose]);

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild onClick={onClickedTrigger}>
          <div
            onClick={onClick}
            onKeyDown={onClick}
            role="button"
            tabIndex={0}
            className="border border-solid border-platinium flex items-center gap-x-2 py-[8.5px] px-[14px] text-sm  text-metal-gray rounded-sm relative cursor-pointer"
          >
            {date?.from && date?.to
              ? `${format(date?.from, 'LLL d')} - ${format(date?.to, 'LLL d, yyyy')}`
              : 'Add Dates'}
            <ChevronDownIcon className="w-4 h-4 text-platinum absolute right-4" />
          </div>
        </PopoverTrigger>

        {open && (
          <PopoverContent
            className="md:min-w-[554px] w-max border border-solid border-english-manor border-opacity-40 p-6"
            side="top"
            align="end"
            sideOffset={-100}
            avoidCollisions={false}
            {...rest}
          >
            <CheckoutDateRangePicker
              onClear={onClear}
              onClose={onClose}
              date={date}
              setDate={setDate}
              availableCalendar={propertyDetails.availableCalendar}
              bookingAvailabilityData={null}
              rentalRates={propertyDetails.rentalRates}
              propertyId={propertyDetails.nrPropertyId}
            />
          </PopoverContent>
        )}
      </Popover>
    </>
  );
};

export default DateRangePickerInput;
