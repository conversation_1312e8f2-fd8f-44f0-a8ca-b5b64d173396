'use client';

import { ReactNode, useEffect } from 'react';

import { useProfile } from '@/contexts/selectors/app-context-selectors';
import useHasMounted from '@/hooks/useHasMounted';
import { PropertyDetails } from '@/types/properties';
import { formatSeoProperty, pushEcommerceDataLayer } from '@/utils/enhancedEcomAnanalytics';
import { formatKlaviyoProperty, KlaviyoEvents, pushKlaviyoData } from '@/utils/klaviyoAnalytics';

type Props = {
  children: ReactNode;
  details: PropertyDetails;
};

const ListingDetailsWrapper = ({ children, details }: Props) => {
  const hasMounted = useHasMounted();
  const { profileData } = useProfile();
  useEffect(() => {
    if (hasMounted) {
      pushEcommerceDataLayer('view_item', [formatSeoProperty(details as any)]);
      pushKlaviyoData(
        KlaviyoEvents.LISTING_DETAIL_VIEWED,
        formatKlaviyoProperty(details as any, profileData?.nrUserId ?? ''),
      );
    }
  }, [details, hasMounted, profileData?.nrUserId]);

  return <>{children}</>;
};

export default ListingDetailsWrapper;
