'use client';

import { memo } from 'react';

import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { useBooking } from '@/contexts/BookingContext';
import { FormattedBookingData } from '@/types/booking';
import { Nullable } from '@/types/common';
import { getDiscountName } from '@/utils/booking';
import { currencyFormatterRound, getStringSingularPlural } from '@/utils/common';

import { addDays, format } from 'date-fns';

type Props = {
  bookingData: Nullable<FormattedBookingData>;
  numberOfNights: number;
};

const PricesAndSummary = ({ bookingData, numberOfNights }: Props) => {
  const { date, bookingAvailabilityData, isPetSelected } = useBooking();
  return (
    <div className="border border-solid border-platinium rounded-2xl p-3.5 my-4 md:my-0">
      <div className="flex items-center justify-between mb-1 text-sm text-metal-gray">
        <Popover>
          <PopoverTrigger asChild>
            <div className="underline cursor-pointer">
              {date?.from &&
                date?.to &&
                `${getStringSingularPlural('night', 'nights', numberOfNights)}`}
            </div>
          </PopoverTrigger>
          <PopoverContent className="!shadow-card-25 p-0">
            <>
              <div className="text-center font-semibold px-4 py-2">Base Price Breakdown</div>
              <Separator className="" />
              <div className="px-4">
                {[...Array(numberOfNights).keys()].map((_n, index) => (
                  <div
                    key={index}
                    className="my-2 flex items-center justify-between text-sm text-metal-gray"
                  >
                    <span>{date?.from ? format(addDays(date.from, _n), 'MM/dd/yyyy') : ''}</span>
                    <span>
                      {currencyFormatterRound.format(bookingData?.averageNightlyRate ?? 0)}
                    </span>
                  </div>
                ))}
              </div>
              <Separator className="" />
              <div className="px-4 py-2 flex items-center justify-between">
                <span className="font-semibold">Total Base Price</span>
                <span>
                  {currencyFormatterRound.format(
                    (bookingData?.averageNightlyRate ?? 0) * numberOfNights,
                  )}
                </span>
              </div>
            </>
          </PopoverContent>
        </Popover>

        <span className="text-base md:text-2xl font-medium text-black">
          {currencyFormatterRound.format((bookingData?.averageNightlyRate ?? 0) * numberOfNights)}
        </span>
      </div>
      <div className="flex items-center justify-between mb-1 text-sm text-metal-gray">
        <Popover>
          <PopoverTrigger asChild>
            <div className="underline cursor-pointer">Nantucket Rentals' Fee</div>
          </PopoverTrigger>
          <PopoverContent className="!shadow-card-25 text-sm text-metal-gray">
            This fee helps Nantucket Rentals provide secure booking experiences, and offer 24/7
            local support throughout your trip. This fee is also less than VRBO and AIRBNB.
          </PopoverContent>
        </Popover>

        <span className="text-base md:text-2xl font-medium text-black">
          {currencyFormatterRound.format(bookingAvailabilityData?.nantucket_fee ?? 0)}
        </span>
      </div>
      {isPetSelected && (
        <div className="flex items-center justify-between mb-1 text-sm text-metal-gray">
          Pet Fee
          <span className="text-base md:text-2xl font-medium text-black">
            {currencyFormatterRound.format(bookingAvailabilityData?.pet_fee ?? 0)}
          </span>
        </div>
      )}
      <div className="flex items-center justify-between mb-1 text-sm text-metal-gray">
        State & Local Taxes
        <span className="text-base md:text-2xl font-medium text-black">
          {currencyFormatterRound.format(bookingData?.stateAndLocalTaxes ?? 0)}
        </span>
      </div>
      {bookingAvailabilityData?.discount_type && bookingAvailabilityData?.rule_based_discount && (
        <>
          <Separator className="my-2" />
          <div className="text-right line-through">
            {currencyFormatterRound.format(bookingData?.totalBeforeDiscount ?? 0)}
          </div>
          <div className="flex items-center justify-between mb-1 text-sm text-metal-gray">
            {getDiscountName(bookingAvailabilityData?.discount_type ?? 'Discount Applied')}
            <span className="text-base md:text-2xl font-medium text-green-500">
              -{currencyFormatterRound.format(bookingAvailabilityData?.rule_based_discount ?? 0)}
            </span>
          </div>
        </>
      )}
      <Separator className="my-2" />
      <div className="flex items-center justify-between text-sm text-metal-gray">
        Total
        <span className="text-base md:text-2xl font-medium text-black">
          {currencyFormatterRound.format(
            (bookingData?.totalWithoutTaxes ?? 0) +
              (isPetSelected ? bookingAvailabilityData?.pet_fee ?? 0 : 0),
          )}
        </span>
      </div>
    </div>
  );
};

export default memo(PricesAndSummary);
