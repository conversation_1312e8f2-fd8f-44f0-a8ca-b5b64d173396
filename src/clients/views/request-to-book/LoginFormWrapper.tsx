'use client';

import { memo, useCallback, useState } from 'react';

import { useGoogleLogin } from '@react-oauth/google';

import { revalidateTagByName } from '@/app/actions/revalidateTag';
import Button from '@/clients/ui/Button';
import SvgEnvelopeIcon from '@/common/assets/svgs/Envelope';
import SvgGoogleIcon from '@/common/assets/svgs/GoogleLogin';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { PHONE_NUMBER_PATTERN } from '@/constants/patterns';
import { useBookingForm } from '@/contexts/BookingFormContext';
import useForm from '@/hooks/useForm';
import { actionGoogleLogin } from '@/services/client-side/registration';
import { generateOTP } from '@/services/server/login';
import { ProgressStatus } from '@/types/common';
import { AuthenticationStep } from '@/types/login';
import { getRandomNumberWithDigits } from '@/utils/common';

import classNames from 'classnames';

const LoginFormWrapper = memo(({ hiddenMd }: { hiddenMd?: boolean }) => {
  const { setShowLoginDialog, setAuthStep, setPhone, setUserId } = useBookingForm();

  const [err, setErr] = useState<any>(null);
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(null);
  const { formState, errors, onChange, preSubmitCheck: preSubmitCheck } = useForm<{
    phone: string;
  }>(
    {
      phone: '',
    },
    {
      phone: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Phone is required.`;
        }

        if (!_value.match(PHONE_NUMBER_PATTERN)) {
          return 'Invalid phone number';
        }
      },
    },
    false,
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange],
  );

  const handleGoogleLogin = useGoogleLogin({
    onSuccess: (tokenResponse) => {
      setErr(null);

      actionGoogleLogin({
        access_token: tokenResponse.access_token,
      })
        .then((data) => {
          console.debug('the data is', data);
          revalidateTagByName(`user-profile`);
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          // onToggle();
        })
        .catch((err) => {
          console.debug('error is', err);
          setErr(err);
          setProgressStatus(ProgressStatus.FAILED);
        });
    },
    onError: (error) => {
      console.log('Login Failed', error);
      setErr(error);
    },
  });

  const handleContinueEmail = useCallback(() => {
    setShowLoginDialog(true);
    setAuthStep(AuthenticationStep.LOGIN);
  }, [setAuthStep, setShowLoginDialog]);

  const onContinue = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== '')) {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    const user_id = getRandomNumberWithDigits(5);
    generateOTP({
      phone_number: `+1${formState.phone}`,
      user_id,
      signup_flow: true,
    })
      .then((data) => {
        console.debug('the data is', data);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        setShowLoginDialog(true);
        setAuthStep(AuthenticationStep.VERIFICATION_CODE_ENTRY);
        setPhone(`+1${formState.phone}`);
        setUserId(user_id);
      })
      .catch((err) => {
        console.debug('error is', err);
        setErr(err);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [formState.phone, preSubmitCheck, setAuthStep, setPhone, setUserId, setShowLoginDialog]);
  return (
    <>
      <div
        className={classNames('trip-login-form h-fit w-full md:w-[500px] pb-4 md:pb-0', {
          'md:hidden': hiddenMd,
        })}
      >
        <Separator className="-ml-4 md:ml-0 w-[calc(100%+32px)] md:w-full my-4 h-1 md:h-[1px]" />
        <p className="font-medium text-sm md:text-base">Log in or sign up to book</p>
        <div className="w-full">
          {err && (
            <div>
              {Object.keys(err).map((_key, index) => (
                <p key={index} className="m-0 mb-2 text-error">
                  {_key} : <span className="font-medium">{err[_key]}</span>
                </p>
              ))}
            </div>
          )}
          <Input
            type="phone"
            name="phone"
            value={formState.phone}
            placeholder="Phone Number"
            className="w-full mt-2 px-3 py-2 rounded-lg"
            onChange={onChangeTextInput}
            helperText={errors?.phone ?? ''}
            error={!!errors?.phone?.length}
            pattern="\d*"
            inputMode="numeric"
          />
          <p className="text-[8px] m-0 text-primary-text leading-[150%] mt-2">
            We&apos;ll text you to confirm your number. Standard message and date rates may apply.
          </p>
          <Button
            className="font-medium w-full rounded-lg px-5 md:px-9 py-2 mt-2"
            onClick={onContinue}
            isLoading={progressStatus === ProgressStatus.LOADING}
          >
            Continue
          </Button>
          <div className="mt-2 relative h-4 flex items-center justify-center">
            <Separator />
            <span className="absolute left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2 text-xs bg-white px-4 text-metal-gray font-medium">
              or
            </span>
          </div>
          <Button
            intent="outline"
            className="w-full rounded-lg px-5 md:px-9 py-2 text-primary-text font-medium leading-[24px] capitalize my-2 border-[1.5px] border-solid border-disabled"
            onClick={() => handleGoogleLogin()}
          >
            <SvgGoogleIcon className="mr-4" />
            Continue With Google
          </Button>
          <Button
            intent="outline"
            className="w-full rounded-lg px-5 md:px-9 py-2 text-primary-text font-medium leading-[24px] capitalize border-[1.5px] border-solid border-disabled"
            onClick={handleContinueEmail}
          >
            <SvgEnvelopeIcon className="mr-4" />
            Continue With Email
          </Button>
        </div>
      </div>
    </>
  );
});

export default LoginFormWrapper;
