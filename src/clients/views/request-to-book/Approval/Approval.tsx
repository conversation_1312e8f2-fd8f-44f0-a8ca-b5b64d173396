'use client';

import React, { useCallback, useEffect } from 'react';

import Button from '@/clients/ui/Button';
import SvgCsvUploaded from '@/common/assets/svgs/CsvUploaded';
import { getBookingDetails } from '@/services/client-side/bookings';
import { getPropertyDetails } from '@/services/client-side/properties';
import { EnhancedEcomItem } from '@/types/properties';
import {
  EnhacedEcomPropertyItem,
  formatSeoProperty,
  pushEcomerenceApprovalDataLayer,
} from '@/utils/enhancedEcomAnanalytics';
import { BOOKING_REQUESTED, KlaviyoEvents, pushKlaviyoData } from '@/utils/klaviyoAnalytics';

import { useSearchParams, useRouter } from 'next/navigation';

const Approval = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const bookingId = searchParams?.get('bookingId');
  const propertyId = searchParams?.get('propertyId');
  const coupon = searchParams?.get('coupon') ?? '';

  const handleMessages = useCallback(() => {
    if (bookingId) {
      router.push(`/messages/${bookingId}`);
    }
  }, [router, bookingId]);

  useEffect(() => {
    if (bookingId && propertyId) {
      Promise.all([
        getBookingDetails<any>(bookingId),
        getPropertyDetails<EnhacedEcomPropertyItem>(propertyId),
      ])
        .then(([bookingData, propertyData]) => {
          let bookingProps = '';

          try {
            const storedData = localStorage.getItem(BOOKING_REQUESTED);
            if (storedData) {
              bookingProps = JSON.parse(storedData);
            }
          } catch (error) {
            console.error('Error parsing stored booking data:', error);
          }

          const tax = Number(bookingData.occupancyTax ?? 0);
          const paymentId = Number(bookingData.payments[0]?.paymentId ?? 0);
          const propertySeoData: EnhancedEcomItem[] = propertyData
            ? [formatSeoProperty(propertyData)]
            : [];

          pushEcomerenceApprovalDataLayer('purchase', propertySeoData, coupon, tax, paymentId);

          setTimeout(() => {
            pushKlaviyoData(KlaviyoEvents.BOOKING_REQUESTED, bookingProps);
          }, 4000);
        })
        .catch((e) => console.error('Error fetching booking details:', e));
    }
  }, [bookingId, coupon, propertyId]);

  return (
    <>
      <div className="text-center max-w-[846px] mx-auto">
        <div className="mb-7.5">
          <SvgCsvUploaded width={100} height={100} />
        </div>
        <p className="m-0 mb-5 text-lg md:text-xl">Rental Agreement has been sent for approval</p>
        <div className="mb-7.5 text-[#6d7380]">
          <p className="m-0 text-xs md:text-sm">
            Your signed Rental Agreement and Payment have been received by Nantucket Rentals. When
            the Rental Agreement is approved and signed by the Homeowner, your payment will be
            processed and your booking will be confirmed.
          </p>
          <br />
          <p className="m-0 text-xs md:text-sm">
            A copy of the fully signed agreement will be emailed to you.
          </p>
          <br />
          <p className="m-0 text-xs md:text-sm">
            If the Homeowner wants to discuss your request you will be notified via email. The
            Messages tool will keep track of all your communication related to this booking and will
            remain open throughout your stay.
          </p>
        </div>
        <div>
          <Button onClick={handleMessages} className="!my-5 !px-10 !py-3 bg-#15A5E5">
            View Your Booking
          </Button>
        </div>
      </div>
    </>
  );
};

export default Approval;
