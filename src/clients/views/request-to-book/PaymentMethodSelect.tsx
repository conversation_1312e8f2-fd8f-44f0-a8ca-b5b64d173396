'use client';

import { memo, useCallback } from 'react';

import Checkbox from '@/components/ui/checkbox';
import { useBookingForm } from '@/contexts/BookingFormContext';
import { BookingPaymentMethod } from '@/types/booking';
import FormHelperText from '@/ui/atoms/FormHelperText';

import Image from 'next/image';

const PaymentMethodSelect = memo(() => {
  const { paymentMethod, setPaymentMethod, errors } = useBookingForm();

  const onChangeMethod = useCallback(
    (method: BookingPaymentMethod) => {
      setPaymentMethod(method);
    },
    [setPaymentMethod],
  );

  return (
    <>
      <div className="flex items-center space-x-2 my-2">
        <Checkbox
          id="ach"
          onChange={() => onChangeMethod(BookingPaymentMethod.ACH)}
          className="rounded-full"
          checked={paymentMethod === BookingPaymentMethod.ACH}
        />
        <label htmlFor="ach" className="cursor-pointer text-sm">
          ACH
        </label>
      </div>
      <div className="flex items-start space-x-2">
        <Checkbox
          id="ach"
          onChange={() => onChangeMethod(BookingPaymentMethod.CREDIT_CARD)}
          className="rounded-full"
          checked={paymentMethod === BookingPaymentMethod.CREDIT_CARD}
        />
        <label htmlFor="ach" className="cursor-pointer text-sm">
          Credit or debit card <span className="text-[8px]">(Processing fees apply)</span>
          <br />
          <Image
            src="/images/payment-cards.svg"
            alt="Payment cards"
            width={109}
            height={16}
            priority
          />
        </label>
      </div>
      {errors['paymentMethod'] && !paymentMethod && (
        <FormHelperText error className="pl-4">
          {errors['paymentMethod']}
        </FormHelperText>
      )}
    </>
  );
});

export default PaymentMethodSelect;
