'use client';

import { memo, useCallback, useState } from 'react';

import toast from 'react-hot-toast';

import BackdropLoader from '@/app/components/common/BackdropLoader';
import ProgressDialog from '@/clients/components/common/ProgressDialog';
import Button from '@/clients/ui/Button';
import { useBookingForm } from '@/contexts/BookingFormContext';
import { useRequestToBook } from '@/contexts/RequestToBookContext';
import { sendBookingRequestForm } from '@/services/client-side/booking-request-form';
import { ProgressStatus } from '@/types/common';
import { PropertyDetails } from '@/types/properties';
import {
  EnhacedEcomPropertyItem,
  formatSeoProperty,
  pushEcommerceDataLayer,
} from '@/utils/enhancedEcomAnanalytics';

import { format } from 'date-fns';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';

const DocusignDialog = dynamic(() => import('./DocusignDialog'), {
  ssr: false,
  loading: () => <BackdropLoader />,
});

const RequestToBookButton = memo(({ property }: { property: PropertyDetails }) => {
  const router = useRouter();
  const [docusignUrl, setDocusignUrl] = useState<string>('');
  const {
    bookingAvailabilityData,
    date,
    guests,
    isPetSelected,
    petCount,
    petDescription,
    petType,
  } = useRequestToBook();
  const {
    message,
    paymentMethod,
    isInsuranceAdded,
    errors,
    setErrors,
    progressStatus,
    setProgressStatus,
  } = useBookingForm();

  const preSubmitCheck = useCallback(() => {
    const newErrors: any = {};
    if (!date?.from || !date?.to) {
      newErrors['date'] = 'Dates are required';
    }

    if (!paymentMethod) {
      newErrors['paymentMethod'] = 'Payment method is a required';
    }

    if (message.trim().length === 0) {
      newErrors['message'] = 'Message is a required field';
    }

    setErrors(newErrors);
    return newErrors;
  }, [date?.from, date?.to, message, paymentMethod, setErrors]);

  const onRquestToBook = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== '')) {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);

    pushEcommerceDataLayer('begin_checkout', [
      formatSeoProperty((property as unknown) as EnhacedEcomPropertyItem),
    ]);
    const origin =
      typeof window !== 'undefined' && window.location.origin ? window.location.origin : '';

    if (bookingAvailabilityData && date?.from && date?.to && paymentMethod) {
      sendBookingRequestForm({
        nrPropertyId: bookingAvailabilityData.nr_property_id,
        checkInDateTime: format(date.from, 'yyyy-MM-dd'),
        checkOutDateTime: format(date.to, 'yyyy-MM-dd'),
        adultsCount: guests?.adults ?? 1,
        childrenCount: guests?.children ?? 0,
        totalGuestCount: (guests?.adults ?? 1) + (guests?.children ?? 0),
        rent: bookingAvailabilityData?.rent ?? 0,
        serviceFee: bookingAvailabilityData?.nantucket_fee ?? 0,
        text: message,
        paymentMethod,
        petsAllowed: isPetSelected,
        return_url: `${origin}/request-to-book/approval`,
        travelInsurance: isInsuranceAdded,
        petCount: isPetSelected ? petCount : undefined,
        petType: isPetSelected ? petType : undefined,
        petDescription: isPetSelected && petDescription.length > 0 ? petDescription : undefined,
      })
        .then((data: any) => {
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          setDocusignUrl(data.redirect_url);
        })
        .catch((error) => {
          const errorMsg = (error as any)?.detail ?? 'Failed to send booking request';
          console.log('error', errorMsg);
          toast.error(errorMsg);
          setProgressStatus(ProgressStatus.FAILED);
        });
    }
  }, [
    bookingAvailabilityData,
    date?.from,
    date?.to,
    guests?.adults,
    guests?.children,
    isInsuranceAdded,
    isPetSelected,
    message,
    paymentMethod,
    petCount,
    petDescription,
    petType,
    preSubmitCheck,
    property,
    setProgressStatus,
  ]);

  const onComplete = useCallback(
    (bookingId: number, propertyId: number, event: string) => {
      router.push(
        `/request-to-book/approval?bookingId=${bookingId}&propertyId=${propertyId}&event=${event}`,
      );
      setDocusignUrl('');
    },
    [router],
  );

  return (
    <>
      <Button
        disabled={!bookingAvailabilityData}
        className="w-full rounded-[32px]"
        isLoading={progressStatus === ProgressStatus.LOADING}
        onClick={onRquestToBook}
      >
        Request to book
      </Button>
      {progressStatus === ProgressStatus.LOADING && !docusignUrl && (
        <ProgressDialog
          open={progressStatus === ProgressStatus.LOADING && !docusignUrl}
          title={'Preparing Rental Agreement ...'}
        />
      )}
      {progressStatus === ProgressStatus.SUCCESSFUL && docusignUrl && docusignUrl?.length > 0 && (
        <DocusignDialog agreementUrl={docusignUrl} onComplete={onComplete} />
      )}
    </>
  );
});

export default RequestToBookButton;
