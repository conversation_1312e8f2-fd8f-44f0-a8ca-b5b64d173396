'use client';

import { memo, useState } from 'react';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import InputLabel from '@/ui/atoms/InputLabel';
import { currencyFormatter } from '@/utils/amenity';

type Props = {
  open: boolean;
  handleClose: () => void;
  setAddedInsurance?: (val: boolean) => void;
  insuranceAmount: number;
  addedInsurance?: boolean;
};

const TravelInsuranceDialog = ({
  open,
  handleClose,
  setAddedInsurance,
  insuranceAmount,
  addedInsurance,
}: Props) => {
  const [expanded, setExpanded] = useState<string | null>(null);

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl px-6 py-5 sm:px-8 sm:py-6 w-[90vw] sm:w-[80vw] h-[90vh] sm:h-auto overflow-y-auto fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rounded-xl">
        <div>
          <h3 className="font-medium m-0 text-lg">Travel insurance</h3>
          <p className="text-sm text-muted-foreground">Provided by Generali Travel Insurance</p>

          <div className="text-sm font-medium mt-4 mb-3">What's covered</div>
          <div className="text-sm text-muted-foreground mb-2 -mt-2.5">
            View complete{' '}
            <a
              href="https://www.csatravelprotection.com/certpolicy.do?product=GR330"
              target="_blank"
              rel="noopener noreferrer"
              className="text-foreground font-medium underline"
            >
              Plan details
            </a>
          </div>

          <Accordion
            type="single"
            collapsible
            value={expanded ?? ''}
            onValueChange={(val) => setExpanded(val === expanded ? '' : val)}
            className="w-full"
          >
            <AccordionItem value="panel1" className="border-b border-border">
              <AccordionTrigger className="flex justify-between items-start py-2 bg-transparent border-none">
                <div className="text-left text-sm">
                  Trip Cancellation
                  <InputLabel className="mb-0">Up to 100% of trip reservation cost</InputLabel>
                </div>
              </AccordionTrigger>
              <AccordionContent className="text-sm text-muted-foreground">
                Get reimbursed if you cancel your Nantucket reservation for covered reason, such as:
                <ul className="pl-4 mt-2 mb-0 list-disc">
                  <li>Illness or injury</li>
                  <li>Flight cancellation or delay due to weather</li>
                  <li>Mandatory evacuation due to weather</li>
                </ul>
              </AccordionContent>
            </AccordionItem>
            <Separator />
            <AccordionItem value="panel2" className="border-b border-border">
              <AccordionTrigger className="flex justify-between items-start py-2 bg-transparent border-none">
                <div className="text-left text-sm">
                  Travel delay
                  <InputLabel className="mb-0">Up to $750 per person</InputLabel>
                </div>
              </AccordionTrigger>
              <AccordionContent className="text-sm text-muted-foreground">
                Get reimbursed for certain additional expenses you incur because your trip is
                delayed for 12 hours or more for a covered reason, such as:
                <ul className="pl-4 mt-2 mb-0 list-disc">
                  <li>Flight delays</li>
                  <li>Passport theft</li>
                  <li>Adverse weather</li>
                </ul>
              </AccordionContent>
            </AccordionItem>
            <Separator />
            <AccordionItem value="panel3" className="border-b border-border">
              <AccordionTrigger className="flex justify-between items-start py-2 bg-transparent border-none">
                <div className="text-left text-sm">
                  Medical
                  <InputLabel className="mb-0">Up to $50,000 per person</InputLabel>
                </div>
              </AccordionTrigger>
              <AccordionContent className="text-sm text-muted-foreground">
                Get reimbursed for doctor-ordered medical services during your trip, such as:
                <ul className="pl-4 mt-2 mb-0 list-disc">
                  <li>Hospital services</li>
                  <li>Ambulance services</li>
                  <li>Prescription medicines</li>
                </ul>
              </AccordionContent>
            </AccordionItem>
            <Separator />
            <AccordionItem value="baggagePanel" className="border-b border-border">
              <AccordionTrigger className="flex justify-between items-start py-2 bg-transparent border-none">
                <div className="text-left text-sm">
                  Baggage
                  <InputLabel className="mb-0">Up to $1,000 per person</InputLabel>
                </div>
              </AccordionTrigger>
              <AccordionContent className="text-sm text-muted-foreground">
                Get reimbursed for:
                <ul className="pl-4 mt-2 mb-0 list-disc">
                  <li>Lost, stolen, or damaged baggage</li>
                  <li>Lost, stolen, or damaged travel documents</li>
                  <li>Unauthorized use of credit cards</li>
                </ul>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          <div className="text-sm font-medium mt-4 mb-3">Additional Benefits</div>
          <div className="text-sm text-muted-foreground">
            Each plan comes with 24-hour emergency hotline, concierge services, and roadside
            assistance — and covers up to 9 people staying with you.
          </div>

          <div className="my-4 w-full h-[1px] bg-border" />

          <div className="text-sm text-muted-foreground">
            The plan cost includes the travel insurance premium and assistance services fee. Travel
            insurance coverages are underwritten by: Generali U.S. Branch, New York, NY; NAIC
            #11231. For more info, see{' '}
            <a
              href="https://www.generalitravelinsurance.com/customer/disclosures.html"
              target="_blank"
              rel="noopener noreferrer"
              className="underline"
            >
              Important Disclosures
            </a>
            .
          </div>

          <div className="flex items-center justify-between mt-6 pt-2 sticky bottom-0 bg-white">
            <Button variant="ghost" onClick={handleClose} className="text-sm px-3 py-1.5 h-auto">
              Cancel
            </Button>
            <Button
              disabled={!!addedInsurance}
              onClick={() => {
                setAddedInsurance?.(true);
                handleClose();
              }}
              className="text-sm px-4 py-1.5 h-auto"
            >
              {`Add for ${currencyFormatter.format(insuranceAmount)}`}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default memo(TravelInsuranceDialog);
