'use client';

import { memo, useCallback } from 'react';

import { Textarea } from '@/components/ui/textarea';
import { useBookingForm } from '@/contexts/BookingFormContext';

const HomeownerMessageInput = memo(() => {
  const { message, setMessage, errors } = useBookingForm();

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { value } = event.target;

      setMessage(value);
    },
    [setMessage],
  );

  return (
    <>
      <Textarea
        name="message"
        value={message}
        placeholder="Example: “Hi, myself, my partner and our two children ages 5 and 7 are coming to Nantucket for a family vacation and love the house!”"
        className="w-full rounded-[10px] my-2 !text-xs"
        rows={3}
        onChange={onChangeTextInput}
        error={!!errors['message'] && message.length === 0}
        helperText={errors['message'] && message.length === 0 ? errors['message'] : ''}
      />
    </>
  );
});

export default HomeownerMessageInput;
