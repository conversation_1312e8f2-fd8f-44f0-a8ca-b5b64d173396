'use client';

import { memo, useCallback, useMemo, useState } from 'react';

import Button from '@/clients/ui/Button';
import { Separator } from '@/components/ui/separator';
import { useBookingForm } from '@/contexts/BookingFormContext';
import { useRequestToBook } from '@/contexts/RequestToBookContext';
import { currencyFormatter } from '@/utils/common';
import { getBookingCalculatedValues } from '@/utils/requestbook';

import { differenceInCalendarDays } from 'date-fns';
import dynamic from 'next/dynamic';

const PriceBreakdownDialog = dynamic(() => import('./PriceBreakdownDialog'), { ssr: false });

const PriceDetails = memo(() => {
  const { bookingAvailabilityData, isPetSelected, date } = useRequestToBook();
  const { isInsuranceAdded } = useBookingForm();
  const { occupancyTax, totalWithoutTaxes, grandTotal } = useMemo(
    () => getBookingCalculatedValues(bookingAvailabilityData, isPetSelected),
    [bookingAvailabilityData, isPetSelected],
  );
  const [showPricesBreakdown, setShowPricesBreakdown] = useState<boolean>(false);
  const numberOfNights = useMemo(
    () => (date?.from && date?.to ? differenceInCalendarDays(date?.to, date?.from) : 0),
    [date?.from, date?.to],
  );

  const onToggle = useCallback(() => {
    setShowPricesBreakdown((_s) => !_s);
  }, []);

  return (
    <>
      <p className="text-sm m-0 font-medium">Price Details</p>
      <div className="flex items-center justify-between mt-2">
        <p className="m-0 text-xs">{numberOfNights} nights</p>
        <p className="m-0 text-xs">{currencyFormatter.format(totalWithoutTaxes ?? 0)}</p>
      </div>
      <div className="flex items-center justify-between mt-2">
        <p className="m-0 text-xs">Taxes</p>
        <p className="m-0 text-xs">{currencyFormatter.format(occupancyTax ?? 0)}</p>
      </div>
      <Separator className="my-4" />
      <div className="flex items-center justify-between mt-2">
        <p className="m-0 text-xs font-semibold">TOTAL (USD)</p>
        <p className="m-0 text-xs font-semibold">
          {currencyFormatter.format(
            Number(grandTotal ?? 0) +
              Number(isInsuranceAdded ? bookingAvailabilityData?.travel_insurance_amount ?? 0 : 0),
          )}
        </p>
      </div>
      <Button intent="ghost" className="underline !p-0 text-xs font-medium" onClick={onToggle}>
        Price breakdown
      </Button>
      {showPricesBreakdown && (
        <PriceBreakdownDialog open={showPricesBreakdown} onToggle={onToggle} />
      )}
    </>
  );
});

export default PriceDetails;
