'use client';

import { memo } from 'react';

import { useRequestToBook } from '@/contexts/RequestToBookContext';
import { PropertyDetails } from '@/types/properties';
import { getStringSingularPlural } from '@/utils/common';

import { format } from 'date-fns';

import EditTripDetailsButton from './EditTripDetailsButton';

type Props = {
  data: PropertyDetails;
};

const TripDetails = memo(({ data }: Props) => {
  const { date, guests, petCount, isPetSelected } = useRequestToBook();
  return (
    <div className="relative">
      <p className="text-sm m-0 font-medium">Trip Details</p>
      <p className="m-0 text-xs mt-1">
        {date?.from ? format(date?.from, 'EEE, LLL d, yyyy') : 'N/A'} -{' '}
        {date?.to ? format(date?.to, 'EEE, LLL d, yyyy') : 'N/A'}
      </p>
      <p className="m-0 text-xs mt-1">
        {getStringSingularPlural('Adult', 'Adults', guests.adults)},{' '}
        {getStringSingularPlural('Child', 'Children', guests.children)}
        {isPetSelected && petCount > 0 && `, ${getStringSingularPlural('Pet', 'Pets', petCount)}`}
      </p>
      <EditTripDetailsButton
        data={data}
        className="absolute right-0 top-0 !p-0 !text-[10px] font-medium uppercase rounded-[20px] w-[70px] h-4 flex items-center justify-center"
        intent="secondary"
        text="Change"
      />
    </div>
  );
});

export default TripDetails;
