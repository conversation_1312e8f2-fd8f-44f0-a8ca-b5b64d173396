'use client';

import { memo, useEffect, useRef } from 'react';

import { Dialog, DialogContent, DialogDescription, DialogTitle } from '@/components/ui/dialog';

import { useSearchParams } from 'next/navigation';

const DocusignDialog = ({
  agreementUrl,
  onComplete,
}: {
  agreementUrl: string;
  onComplete?: (bookingId: number, propertyId: number, event: string) => void;
}) => {
  const searchParams = useSearchParams();
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    const checkCompletion = () => {
      const iframe = iframeRef.current;
      if (!iframe) return;

      try {
        const iframeUrl = iframe.contentWindow?.location.href;
        if (iframeUrl?.includes(`approval`)) {
          console.log('Checking iframe URL:', iframeUrl);

          // Parse the iframe URL to get parameters
          const iframeUrlObj = new URL(iframeUrl);
          const iframeParams = new URLSearchParams(iframeUrlObj.search);

          const bookingId = iframeParams.get('bookingId');
          const propertyId = iframeParams.get('propertyId');
          const event = iframeParams.get('event');

          onComplete?.(Number(bookingId), Number(propertyId), event ?? '');
        }
      } catch (error) {
        // Expected error due to same-origin policy
        console.error(error);
      }
    };

    const intervalId = setInterval(checkCompletion, 1000);

    return () => {
      clearInterval(intervalId);
    };
  }, [agreementUrl, onComplete, searchParams]);

  return (
    <Dialog open>
      <DialogContent
        className="p-0 h-[96dvh] md:h-[95dvh] max-h-screen md:max-w-[96dvw] overflow-y-hidden !rounded-sm"
        hideCloseButton
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogTitle className="hidden" />
        <DialogDescription className="hidden" />
        <div className="w-full h-full pt-safe-top pb-safe-bottom">
          <iframe
            ref={iframeRef}
            id="DocusignIframe"
            className="w-full h-full z-[99999999] border-0"
            title="Docusign Agreement"
            src={agreementUrl}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default memo(DocusignDialog);
