'use client';

import { memo } from 'react';

import { useRequestToBook } from '@/contexts/RequestToBookContext';
import { PropertyDetails } from '@/types/properties';
import { getStringSingularPlural } from '@/utils/common';

import { format } from 'date-fns';

import EditTripDetailsButton from './EditTripDetailsButton';

type Props = {
  data: PropertyDetails;
};

const TripDetailsWrapper = memo(({ data }: Props) => {
  const { date, guests, petCount, isPetSelected } = useRequestToBook();
  return (
    <>
      <p className="hidden lg:block leading-7 font-semibold">Trip Details</p>
      <div className="flex items-center justify-between md:w-[500px] my-2">
        <div>
          <p className="m-0 text-primary-text font-medium text-sm">Dates</p>
          <p className="text-xs m-0">
            {date?.from && format(date.from, 'LLL d, yyyy')} -{' '}
            {date?.to && format(date.to, 'LLL d, yyyy')}
          </p>
        </div>
        <EditTripDetailsButton data={data} />
      </div>
      <div className="flex items-center justify-between md:w-[500px] my-2">
        <div>
          <p className="m-0 text-primary-text font-medium text-sm">Guests</p>
          <p className="text-xs m-0">
            {getStringSingularPlural('Adult', 'Adults', guests.adults)},{' '}
            {getStringSingularPlural('Child', 'Children', guests.children)}
            {isPetSelected &&
              petCount > 0 &&
              `, ${getStringSingularPlural('Pet', 'Pets', petCount)}`}
          </p>
        </div>
        <EditTripDetailsButton data={data} defaultTab="guests" />
      </div>
    </>
  );
});

export default TripDetailsWrapper;
