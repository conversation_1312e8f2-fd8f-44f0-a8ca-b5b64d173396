import { BookingAvailabilityData, FormattedBookingData } from '@/types/booking';

import { format } from 'date-fns';

export const formatBookingFeesData = (
  bookingAvailabilityData: BookingAvailabilityData,
  petQuestion: string,
  nights: number,
): FormattedBookingData => {
  const rent = Number(bookingAvailabilityData?.rent ?? 0);
  const averageNightlyRate = Number(
    ((bookingAvailabilityData?.rent ?? 0) + (bookingAvailabilityData?.rule_based_discount ?? 0)) /
      nights,
  );
  const pet_fee = petQuestion === 'yes' ? Number(bookingAvailabilityData?.pet_fee ?? 0) : 0;
  const otherFees = Number(bookingAvailabilityData?.other_fees ?? 0);
  const nantucketRentalsFee = Number(bookingAvailabilityData?.nantucket_fee ?? 0); // Nantucket Rentals Fee = 10% of Rent
  // if charge_community_impact_fee is true then additional 3%(ie. 14.7%) tax will be applied along with existing 11.7%
  const occupancyTaxPercentage = bookingAvailabilityData?.charge_community_impact_fee
    ? 0.147
    : 0.117;
  const occupancyTax =
    bookingAvailabilityData?.occupancy_tax === 0
      ? 0
      : (rent + nantucketRentalsFee + otherFees + pet_fee) * occupancyTaxPercentage; // Occupancy Tax = 11.7% of (Rent + Nantucket Rentals Fee + Other Fees)
  const stateAndLocalTaxes =
    0.117 * rent + (bookingAvailabilityData.charge_community_impact_fee ? 0.03 * rent : 0);
  const ownerFee = otherFees + pet_fee;
  const totalBeforeDiscount =
    averageNightlyRate * nights + nantucketRentalsFee + ownerFee + stateAndLocalTaxes;
  const totalWithoutTaxes =
    rent +
    nantucketRentalsFee +
    ownerFee +
    stateAndLocalTaxes -
    (bookingAvailabilityData?.rule_based_discount ?? 0);
  const grandTotal = totalWithoutTaxes + occupancyTax;

  return {
    occupancyTax,
    totalWithoutTaxes,
    grandTotal,
    nantucketRentalsFee,
    averageNightlyRate,
    totalBeforeDiscount,
    stateAndLocalTaxes,
  };
};

export const getDiscountName = (discountName = 'Discount Applied') => {
  if (discountName === 'Custom Discount') {
    return 'Discount Applied';
  } else {
    return discountName;
  }
};

type BuildBookingUrlParams = {
  slug: string;
  adults: number;
  children: number;
  from: Date;
  to: Date;
  petCount?: number;
  petType?: string;
  petDescription?: string;
  baseUrl?: string;
};

export function buildBookingUrl({
  slug,
  adults,
  children,
  from,
  to,
  petCount,
  petType,
  petDescription,
  baseUrl = '',
}: BuildBookingUrlParams): string {
  const url = new URL(`/request-to-book/${slug}`, baseUrl || window.location.origin);

  url.searchParams.set('adults', adults.toString());
  url.searchParams.set('children', children.toString());
  if (petCount && petType) {
    url.searchParams.set('petCount', petCount.toString());
    url.searchParams.set('petType', petType);
  }
  if (petDescription) {
    url.searchParams.set('petDescription', petDescription);
  }
  url.searchParams.set('from', format(from, 'yyyy-MM-dd'));
  url.searchParams.set('to', format(to, 'yyyy-MM-dd'));

  return url.pathname + url.search;
}
