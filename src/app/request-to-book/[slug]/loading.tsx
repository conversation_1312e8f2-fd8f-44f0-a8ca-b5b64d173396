import React from 'react';

import { ArrowLeftIcon } from '@heroicons/react/24/outline';

import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@mui/material';

const RequestToBookSkeleton = () => {
  return (
    <main>
      <div className="container min-h-[80dvh] py-6 md:py-10">
        <p className="m-0 text-lg md:text-[30px] text-carolina-blue font-medium mb-5 flex items-center">
          <span className="text-current mr-4 flex self-center">
            <ArrowLeftIcon className="h-6 w-6" />
          </span>
          Request Your Booking
        </p>
        <div className="request-to-book-wrapper grid grid-cols-1 gap-x-2 lg:grid-cols-[3fr_2fr]">
          {/* Left column */}
          <div className="hidden md:block">
            <div className="trip-details h-fit md:block">
              <div className="flex flex-col space-y-2">
                <Skeleton variant="rectangular" height={40} width="60%" />
                <div className="flex space-x-4">
                  <Skeleton variant="rectangular" height={32} width={120} />
                  <Skeleton variant="rectangular" height={32} width={120} />
                </div>
              </div>
              <Separator className="my-4 md:w-[500px]" />
            </div>
            <div className="trip-payment-schedule h-fit">
              <Skeleton variant="rectangular" height={24} width="40%" sx={{ marginBottom: 2 }} />
              <div className="flex flex-col space-y-2">
                <Skeleton variant="rectangular" height={60} width="100%" />
                <Skeleton variant="rectangular" height={60} width="100%" />
              </div>
            </div>

            {/* Form skeleton */}
            <div className="mt-4">
              <Skeleton variant="rectangular" height={40} width="50%" sx={{ marginBottom: 2 }} />
              <div className="flex flex-col space-y-3">
                <Skeleton variant="rectangular" height={56} width="100%" />
                <Skeleton variant="rectangular" height={56} width="100%" />
                <Skeleton variant="rectangular" height={56} width="100%" />
                <Skeleton variant="rectangular" height={100} width="100%" />
                <Skeleton variant="rectangular" height={56} width="100%" />
              </div>
            </div>
          </div>

          {/* Mobile view payment schedule */}
          <div className="trip-payment-schedule h-fit md:hidden">
            <Separator className="-ml-4 w-[calc(100%+32px)] my-4 h-1" />
            <Skeleton variant="rectangular" height={24} width="40%" sx={{ marginBottom: 2 }} />
            <div className="flex flex-col space-y-2">
              <Skeleton variant="rectangular" height={60} width="100%" />
              <Skeleton variant="rectangular" height={60} width="100%" />
            </div>
          </div>

          {/* Mobile form skeleton */}
          <div className="md:hidden mt-4">
            <Skeleton variant="rectangular" height={40} width="50%" sx={{ marginBottom: 2 }} />
            <div className="flex flex-col space-y-3">
              <Skeleton variant="rectangular" height={56} width="100%" />
              <Skeleton variant="rectangular" height={56} width="100%" />
              <Skeleton variant="rectangular" height={56} width="100%" />
              <Skeleton variant="rectangular" height={100} width="100%" />
              <Skeleton variant="rectangular" height={56} width="100%" />
            </div>
          </div>

          {/* Right column */}
          <div className="hidden md:block h-fit border-solid border-grey-border rounded-[10px] p-3">
            {/* Property details skeleton */}
            <div className="flex mb-4">
              <Skeleton variant="rectangular" height={80} width={120} sx={{ marginRight: 2 }} />
              <div className="flex flex-col justify-between">
                <Skeleton variant="rectangular" height={24} width={180} />
                <Skeleton variant="rectangular" height={20} width={150} />
                <Skeleton variant="rectangular" height={20} width={100} />
              </div>
            </div>

            <Separator className="w-full my-4" />

            {/* Trip details skeleton */}
            <div className="mb-4">
              <Skeleton variant="rectangular" height={24} width="40%" sx={{ marginBottom: 2 }} />
              <div className="flex justify-between mb-2">
                <Skeleton variant="rectangular" height={20} width="30%" />
                <Skeleton variant="rectangular" height={20} width="30%" />
              </div>
              <div className="flex justify-between mb-2">
                <Skeleton variant="rectangular" height={20} width="30%" />
                <Skeleton variant="rectangular" height={20} width="30%" />
              </div>
            </div>

            <Separator className="w-full my-4" />

            {/* Price details skeleton */}
            <div>
              <Skeleton variant="rectangular" height={24} width="40%" sx={{ marginBottom: 2 }} />
              <div className="flex justify-between mb-2">
                <Skeleton variant="rectangular" height={20} width="40%" />
                <Skeleton variant="rectangular" height={20} width="20%" />
              </div>
              <div className="flex justify-between mb-2">
                <Skeleton variant="rectangular" height={20} width="40%" />
                <Skeleton variant="rectangular" height={20} width="20%" />
              </div>
              <div className="flex justify-between mb-2">
                <Skeleton variant="rectangular" height={20} width="40%" />
                <Skeleton variant="rectangular" height={20} width="20%" />
              </div>
              <Separator className="w-full my-2" />
              <div className="flex justify-between">
                <Skeleton variant="rectangular" height={24} width="40%" />
                <Skeleton variant="rectangular" height={24} width="20%" />
              </div>
            </div>
          </div>

          {/* Mobile property details */}
          <div className="md:hidden trip-property-details h-fit mt-4">
            <div className="flex mb-4">
              <Skeleton variant="rectangular" height={80} width={120} sx={{ marginRight: 2 }} />
              <div className="flex flex-col justify-between">
                <Skeleton variant="rectangular" height={24} width={180} />
                <Skeleton variant="rectangular" height={20} width={150} />
                <Skeleton variant="rectangular" height={20} width={100} />
              </div>
            </div>
          </div>

          {/* Mobile trip details */}
          <div className="trip-details-right h-fit rounded-[10px] md:hidden">
            <Separator className="-ml-4 md:ml-0 w-[calc(100%+32px)] my-4 h-1" />
            <Skeleton variant="rectangular" height={24} width="40%" sx={{ marginBottom: 2 }} />
            <div className="flex justify-between mb-2">
              <Skeleton variant="rectangular" height={20} width="30%" />
              <Skeleton variant="rectangular" height={20} width="30%" />
            </div>
            <div className="flex justify-between mb-2">
              <Skeleton variant="rectangular" height={20} width="30%" />
              <Skeleton variant="rectangular" height={20} width="30%" />
            </div>
          </div>

          {/* Mobile price details */}
          <div className="trip-price-details-right h-fit rounded-[10px] md:hidden">
            <Separator className="-ml-4 md:ml-0 w-[calc(100%+32px)] my-4 h-1" />
            <Skeleton variant="rectangular" height={24} width="40%" sx={{ marginBottom: 2 }} />
            <div className="flex justify-between mb-2">
              <Skeleton variant="rectangular" height={20} width="40%" />
              <Skeleton variant="rectangular" height={20} width="20%" />
            </div>
            <div className="flex justify-between mb-2">
              <Skeleton variant="rectangular" height={20} width="40%" />
              <Skeleton variant="rectangular" height={20} width="20%" />
            </div>
            <div className="flex justify-between mb-2">
              <Skeleton variant="rectangular" height={20} width="40%" />
              <Skeleton variant="rectangular" height={20} width="20%" />
            </div>
            <Separator className="w-full my-2" />
            <div className="flex justify-between">
              <Skeleton variant="rectangular" height={24} width="40%" />
              <Skeleton variant="rectangular" height={24} width="20%" />
            </div>
          </div>
        </div>
      </div>

      {/* Footer skeleton */}
      <div className="bg-white border-t border-grey-border py-4">
        <div className="container flex justify-between items-center">
          <div className="hidden md:block">
            <Skeleton variant="rectangular" height={24} width={200} />
          </div>
          <div className="flex-1 md:flex-none">
            <Skeleton
              variant="rectangular"
              height={48}
              width="100%"
              sx={{ maxWidth: '300px', marginLeft: 'auto' }}
            />
          </div>
        </div>
      </div>
    </main>
  );
};

export default RequestToBookSkeleton;
