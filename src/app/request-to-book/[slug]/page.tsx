import { Suspense } from 'react';

import { ArrowLeftIcon } from '@heroicons/react/24/outline';

import BookingPropertyDetails from '@/app/views/request-to-book/BookingPropertyDetails';
import RequestToBookFooter from '@/app/views/request-to-book/RequestToBookFooter';
import RequestToBookFormWrapper from '@/app/views/request-to-book/RequestToBookFormWrapper';
import PaymentSchedule from '@/clients/views/request-to-book/PaymentSchedule';
import PriceDetails from '@/clients/views/request-to-book/PriceDetails';
import TripDetails from '@/clients/views/request-to-book/TripDetails';
import TripDetailsWrapper from '@/clients/views/request-to-book/TripDetailsWrapper';
import { Separator } from '@/components/ui/separator';
import { BookingFormContextProvider } from '@/contexts/BookingFormContext';
import { RequestToBookContextProvider } from '@/contexts/RequestToBookContext';
import { getUserProfile } from '@/services/server/profile';
import { getPropertyDetailsBySlug } from '@/services/server/properties';
import { ProfileData } from '@/types/profile';
import { PropertyDetails } from '@/types/properties';

import classNames from 'classnames';
import { Metadata } from 'next';
import Link from 'next/link';
import { notFound, redirect } from 'next/navigation';

type PageProps = {
  params: Promise<{
    slug: string | string[];
  }>;
  searchParams: Promise<{
    adults: string;
    children: string;
    from: string;
    to: string;
    petCount: string;
    petType: string;
    petDescription: string;
  }>;
};

export const metadata: Metadata = {
  title: 'Request To Book | Nantucket Rentals',
  description: 'Request to book on the Nantucket Island',
  metadataBase: new URL('https://nantucketrentals.com/'),
  robots: 'index,follow',
};

export default async function RequestToBookPage({ params, searchParams }: PageProps) {
  const { slug } = await params;
  const { adults, children, from, to, petCount, petType, petDescription } = await searchParams;
  if (!adults || !children || !from || !to) {
    redirect(`/${slug}`);
  }
  try {
    const [data, { data: userData }] = await Promise.all([
      getPropertyDetailsBySlug<PropertyDetails | undefined>(slug as string),
      getUserProfile<{ data: ProfileData }>(),
    ]);

    if (!data) {
      return notFound();
    }

    return (
      <RequestToBookContextProvider
        nrPropertyId={data.nrPropertyId}
        property={data}
        searchParams={{
          adults,
          children,
          from,
          to,
          petCount,
          petType,
          petDescription,
        }}
      >
        <BookingFormContextProvider>
          <main>
            <div className="container min-h-[80dvh] py-6 md:py-10">
              <p className="m-0 text-lg md:text-[30px] text-carolina-blue font-medium mb-5 flex items-center">
                <Link href={`/${data.slug}`} className="text-current mr-4 flex self-center">
                  <ArrowLeftIcon className="h-6 w-6" />
                </Link>
                Request Your Booking
              </p>
              <div className="request-to-book-wrapper grid grid-cols-1 gap-x-2 lg:grid-cols-[3fr_2fr]">
                <div className="hidden md:block">
                  <div
                    className={classNames('trip-details h-fit hidden', { 'md:block': !userData })}
                  >
                    <TripDetailsWrapper data={data} />
                    <Separator className="my-4 md:w-[500px]" />
                  </div>
                  <div className="trip-payment-schedule h-fit ">
                    <Suspense fallback={'Loading..'}>
                      <PaymentSchedule isAuthenticated={!!userData} />
                    </Suspense>
                  </div>
                  <RequestToBookFormWrapper userData={userData} property={data} />
                </div>

                <div className="trip-payment-schedule h-fit md:hidden">
                  <Separator className="-ml-4 w-[calc(100%+32px)] my-4 h-1" />
                  <Suspense fallback={'Loading..'}>
                    <PaymentSchedule isAuthenticated={!!userData} />
                  </Suspense>
                </div>
                <RequestToBookFormWrapper userData={userData} property={data} hiddenMd />
                <div className="hidden md:block h-fit border-solid border-grey-border rounded-[10px] p-3">
                  <BookingPropertyDetails data={data} />
                  <div
                    className={classNames({
                      hidden: !userData,
                    })}
                  >
                    <Separator className="w-full my-4" />
                    <TripDetails data={data} />
                  </div>
                  <Separator className="w-full my-4" />
                  <PriceDetails />
                </div>
                <div className="md:hidden trip-property-details h-fit">
                  <BookingPropertyDetails data={data} />
                </div>
                <div className="trip-details-right h-fit rounded-[10px] md:hidden">
                  <Separator className="-ml-4 md:ml-0 w-[calc(100%+32px)] my-4 h-1" />
                  <TripDetails data={data} />
                </div>
                <div className="trip-price-details-right h-fit rounded-[10px] md:hidden">
                  <Separator className="-ml-4 md:ml-0 w-[calc(100%+32px)] my-4 h-1" />
                  <PriceDetails />
                </div>
              </div>
            </div>
            <RequestToBookFooter />
          </main>
        </BookingFormContextProvider>
      </RequestToBookContextProvider>
    );
  } catch (error) {
    console.error(error);
    throw new Error('Something went wrong while loading the page.');
  }
}
