import { ChatBubbleLeftEllipsisIcon, PhoneIcon } from '@heroicons/react/24/outline';

import IntercomWrapper from '@/clients/components/common/IntercomWrapper';

import dayjs from 'dayjs';
import Link from 'next/link';

const RequestToBookFooter = () => {
  return (
    <>
      <div className="bg-[#074059]">
        <div className="container py-5 text-[#DBE5EA]">
          <div className="text-lg font-medium mb-[14px]">For help with your booking</div>
          <a
            className="text-sm flex items-center mb-1 text-inherit no-underline"
            href="tel:(*************"
          >
            <PhoneIcon className="w-4 h-4 mr-2.5" />
            (*************
          </a>
          <IntercomWrapper className="cursor-pointer">
            <ChatBubbleLeftEllipsisIcon className="w-4 h-4 mr-2.5" /> Chat with us
          </IntercomWrapper>
        </div>
      </div>
      <div className="bg-[#002839] px-[50px] py-4">
        <div className="container">
          <div className="flex items-center justify-center flex-col md:flex-row text-xs">
            <div className="mb-2 md:mb-0">
              <div className="text-center leading-[175%] text-white flex items-center flex-row ml-5">
                Copyright © {dayjs().year()} nantucketrentals.com - All rights reserved.
              </div>
            </div>
            <div className="flex flex-row">
              <div className="text-center leading-[175%] text-white flex items-center flex-row ml-5">
                <div className="w-[5px] h-[5px] bg-white rounded-full mr-[10px]" />
                <Link prefetch={true} className="no-underline text-inherit" href="/info/copyright">
                  Copyright
                </Link>
              </div>

              <div className="text-center leading-[175%] text-white flex items-center flex-row ml-5">
                <div className="w-[5px] h-[5px] bg-white rounded-full mr-[10px]" />
                <Link
                  prefetch={true}
                  className="no-underline text-inherit"
                  href="/info/privacy-policy"
                >
                  Privacy Policy
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default RequestToBookFooter;
