import { GoogleOAuthProvider } from '@react-oauth/google';

import BookingTravelInsurance from '@/clients/views/request-to-book/BookingTravelInsurance';
import HomeownerMessageInput from '@/clients/views/request-to-book/HomeownerMessageInput';
import LoginFormWrapper from '@/clients/views/request-to-book/LoginFormWrapper';
import PaymentMethodSelect from '@/clients/views/request-to-book/PaymentMethodSelect';
import RequestToBookButton from '@/clients/views/request-to-book/RequestToBookButton';
import { Separator } from '@/components/ui/separator';
import { ProfileData } from '@/types/profile';
import { PropertyDetails } from '@/types/properties';

import classNames from 'classnames';

const GOOGLE_CLIENT_ID = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID ?? '';

if (!GOOGLE_CLIENT_ID) {
  console.warn('⚠️ Google Client ID is missing in environment variables.');
}

type Props = {
  userData?: ProfileData;
  property: PropertyDetails;
  hiddenMd?: boolean;
};

const RequestToBookFormWrapper = ({ userData, property, hiddenMd }: Props) => {
  return (
    <>
      {userData ? (
        <>
          <div
            className={classNames(
              'trip-payment-method h-fit order-4 w-full md:w-[500px] md:border-solid md:border-grey-border rounded-[10px] md:p-3 md:mb-5',
              {
                'md:hidden': hiddenMd,
              },
            )}
          >
            <Separator
              className={classNames(
                'my-4 md:w-[500px] -ml-4 md:ml-0 w-[calc(100%+32px)] h-1 md:h-[1px]',
                {
                  'md:hidden': userData,
                },
              )}
            />
            <p className="m-0 font-semibold text-sm">Payment Method</p>
            <p className="m-0 text-[10px]">Payment information will be taken on the next page.</p>
            <Separator className="my-2 hidden md:block" />
            <PaymentMethodSelect />
          </div>

          <div
            className={classNames(
              'trip-homeowner-message h-fit order-5 w-full md:w-[500px] md:border-solid md:border-grey-border rounded-[10px] md:p-3 md:mb-5',
              {
                'md:hidden': hiddenMd,
              },
            )}
          >
            <Separator className="-ml-4 w-[calc(100%+32px)] my-4 h-1 md:hidden" />
            <p className="m-0 font-semibold text-sm">Include a message for the homeowner</p>
            <p className="m-0 text-[10px]">
              Let them know who you are traveling with and what brings you to Nantucket.
              <span className="text-error">*</span>
            </p>
            <HomeownerMessageInput />
          </div>
          <BookingTravelInsurance hiddenMd={hiddenMd} />
          <div
            className={classNames('trip-submit-button h-fit order-8 w-full md:w-[500px]', {
              'md:hidden': hiddenMd,
            })}
          >
            <p className="text-center text-[10px] my-4">
              You will not be charged yet. You will review the rental agreement on the next page.
              You&apos;ll only be charged once the request is accepted by the homeowner.
            </p>
            <Separator className="mb-4" />
            <RequestToBookButton property={property} />
          </div>
        </>
      ) : (
        <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
          <LoginFormWrapper hiddenMd={hiddenMd} />
        </GoogleOAuthProvider>
      )}
    </>
  );
};

export default RequestToBookFormWrapper;
