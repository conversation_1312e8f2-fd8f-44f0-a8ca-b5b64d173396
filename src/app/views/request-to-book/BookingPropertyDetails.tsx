import { PropertyDetails } from '@/types/properties';

import Image from 'next/image';

type Props = {
  data: PropertyDetails;
};

const BookingPropertyDetails = ({ data }: Props) => {
  return (
    <div className="">
      <div className="flex gap-x-3">
        <Image
          src={data.nrPropertyPics[0]?.nrPropertyPicPath ?? 'https://fakeimg.pl/300/'}
          alt="Property cover"
          width={0}
          height={0}
          className="w-[160px] h-auto rounded-[10px] object-cover object-center"
          sizes="(max-width: 768px) 50vw, 20vw"
          placeholder="blur"
          blurDataURL="https://via.placeholder.com/150"
        />
        <div className="py-2">
          <p className="m-0 text-sm font-medium mb-4">{data.headline}</p>
          {!data?.managedByOwner && data?.listingId?.startsWith('cnc_') ? (
            <div className="flex gap-x-2">
              <p className="m-0 text-[10px]">Hosted by</p>
              <Image
                width={95}
                height={24}
                alt="CNC logo"
                src="/images/cnc-logo-2.png"
                className=""
              />
            </div>
          ) : (
            <>
              <p className="m-0 text-xs font-medium">Hosted by the homeowner.</p>
              <p className="m-0 text-[10px]">Verified by Nantucket Rentals</p>
            </>
          )}
        </div>
      </div>
      <p className="text-[10px] m-0">
        <span className="text-xs md:text-sm leading-7 font-medium">Free cancellation</span> <br />
        Get a full refund if you cancel before the booking is confirmed. After confirmation, a
        refund is issued if the dates are rebooked.
      </p>
    </div>
  );
};

export default BookingPropertyDetails;
