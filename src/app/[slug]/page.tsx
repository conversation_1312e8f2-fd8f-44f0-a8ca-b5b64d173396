import { ChatBubbleOvalLeftIcon, PhoneIcon } from '@heroicons/react/24/outline';

import Header from '@/app/components/common/Header';
import { H1, H2 } from '@/app/ui/common/Typography';
import Amenities from '@/app/views/listing-details/Amenities';
import ExpandableText from '@/clients/components/common/ExpandableText';
import AllSleepingAreasTrigger from '@/clients/views/listing-details/AllSleepingAreasTrigger';
import AskQuestionButton from '@/clients/views/listing-details/AskAQuestion/AskQuestionButton';
import ListingDetailsWrapper from '@/clients/views/listing-details/ListingDetailsWrapper';
import MobileCheckAvaialability from '@/clients/views/listing-details/MobileCheckAvaialability';
import RequestToBookForm from '@/clients/views/listing-details/RequestToBookForm';
import SvgBathroom from '@/common/assets/svgs/Bathroom';
import Bedroom from '@/common/assets/svgs/Bedroom';
import SvgGuests from '@/common/assets/svgs/Guests';
import SvgPatio from '@/common/assets/svgs/Patio';
import SvgPropertyVerification from '@/common/assets/svgs/PropertyVerification';
import { Separator } from '@/components/ui/separator';
import { BookingContextProvider } from '@/contexts/BookingContext';
import { getUserProfile } from '@/services/server/profile';
import { getPropertyDetailsBySlug } from '@/services/server/properties';
import { Nullable } from '@/types/common';
import { ProfileData } from '@/types/profile';
import { PropertyDetails } from '@/types/properties';
import { getOrdinalRules, getStringSingularPlural } from '@/utils/common';
import { GoogleMapsEmbed } from '@next/third-parties/google';

import { Metadata } from 'next';
import Image from 'next/image';
import { notFound } from 'next/navigation';

import HouseRules from '../views/listing-details/HouseRules';
import ListingHeroImages from '../views/listing-details/ListingHeroImages';

const MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '';

type PageProps = {
  params: Promise<{ slug: string | string[] }>;
  searchParams: Promise<{
    adults: string;
    children: string;
    from: string;
    to: string;
    petCount: string;
    petType: string;
    petDescription: string;
  }>;
};

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { slug } = await params;

  const data = await getPropertyDetailsBySlug<Nullable<PropertyDetails>>(slug as string);

  return {
    title: data?.titleTag ?? '',
    description: data?.metaDescription ?? '',
    metadataBase: new URL('https://nantucketrentals.com/'),
    alternates: {
      canonical: data?.metaCanonical ?? '',
    },
    robots: data?.metaRobots ?? '',
    openGraph: data
      ? {
          images: [data.nrPropertyPics[0].nrPropertyPicPath],
          title: `${data.neighborhood} · ${data.totalBedrooms} bedrooms · Sleeps ${data.totalCapacity}`,
          description: data?.headline ?? '',
          type: 'website',
          siteName: 'Nantucket Rentals',
          url: data?.metaCanonical ?? '',
        }
      : undefined,
  };
}

export default async function PropertyDetailsPage({ params, searchParams }: PageProps) {
  const { slug } = await params;
  const { adults, children, from, to } = await searchParams;
  try {
    const [data, { data: userData }] = await Promise.all([
      getPropertyDetailsBySlug<PropertyDetails | undefined>(slug as string),
      getUserProfile<{ data: ProfileData }>(),
    ]);

    if (!data) {
      return notFound();
    }

    return (
      <BookingContextProvider
        property={data}
        nrPropertyId={data.nrPropertyId}
        searchParams={{
          adults,
          children,
          from,
          to,
        }}
      >
        <ListingDetailsWrapper details={data}>
          <div className="bg-[#F0F9FF] border-solid border-0 border-b-[1px] border-b-[#E0F2FE]">
            <div className="container flex items-center justify-center gap-x-3 py-2.5">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="17"
                height="17"
                viewBox="0 0 17 17"
                fill="none"
                className="hidden lg:block"
              >
                <path
                  d="M9.6534 4.5H14.9867"
                  stroke="#0EA5E9"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M12.3201 1.83333L14.9867 4.5L12.3201 7.16667"
                  stroke="#0EA5E9"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M9.5414 11.5453C9.82501 11.6756 10.161 11.5916 10.3501 11.3433L10.5867 11.0333C10.8385 10.6976 11.2337 10.5 11.6534 10.5H13.6534C14.3893 10.5 14.9867 11.0974 14.9867 11.8333V13.8333C14.9867 14.5692 14.3893 15.1667 13.6534 15.1667C7.03042 15.1667 1.6534 9.78965 1.6534 3.16667C1.6534 2.43029 2.25036 1.83333 2.98674 1.83333H4.98673C5.72262 1.83333 6.32007 2.43078 6.32007 3.16667V5.16667C6.32007 5.58634 6.12248 5.98153 5.78674 6.23333L5.47474 6.46733C5.22238 6.66002 5.14094 7.00394 5.28007 7.28933C6.19119 9.13991 7.68968 10.6365 9.5414 11.5453"
                  stroke="#0EA5E9"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <p className="text-sm text-[#334155] m-0 hidden lg:block">
                Questions or ready to book? Speak to a local specialist now:
              </p>
              <a
                href="tel:************"
                className="flex items-center justify-center px-3 py-[5px] border-solid border-[#BAE6FD] gap-x-2 rounded-full bg-white no-underline text-[#0F172A]"
              >
                <PhoneIcon className="w-4 h-4" />
                <span className="text-sm font-medium">************</span>
              </a>
            </div>
          </div>
          <Header />
          <main>
            <ListingHeroImages
              nrPropertyId={data.nrPropertyId}
              nrPropertyPics={data?.nrPropertyPics}
            />
            <div className="container xl:p-0">
              <div className="flex gap-x-6 pt-4 md:pt-0 md:pb-20">
                <div className="w-full md:flex-grow md:w-[calc(100%-480px)]">
                  <H1 className="font-medium m-0 leading-normal mb-2">{data.headline}</H1>
                  <p className="m-0 pl-2 md:pl-0 text-english-manor italic">
                    NR listing {data.nrPropertyId}
                  </p>
                  <Separator className="my-2 md:my-4" />
                  <div className="flex items-center justify-center space-x-4">
                    <Image
                      alt="NR logo"
                      src="/images/logo.svg"
                      width={0}
                      height={0}
                      className="h-[33px] md:h-[50px] w-auto -mr-[26px] md:-mr-[44px]"
                    />
                    <span className="text-xl">X</span>
                    <Image
                      alt="CNC logo"
                      src="/images/cnc-logo.svg"
                      width={0}
                      height={0}
                      className="h-[33px] md:h-[50px] w-auto"
                    />
                  </div>
                  <div className="px-4 py-3 bg-[#F0F9FF] border-solid border-[#BAE6FD] my-4 rounded-xl flex items-center justify-between flex-col md:flex-row">
                    <div className="flex items-center gap-x-3 max-w-full md:max-w-[50%] my-4 md:my-0">
                      <PhoneIcon className="h-5 w-5 text-carolina-blue hidden md:block" />
                      <span className="text-sm text-[#475569]">
                        Got questions about dates, rates, or policies?
                      </span>
                    </div>
                    <div className="flex items-center justify-between w-full md:w-[50%]">
                      <a
                        href="tel:************"
                        className="flex items-center justify-center px-3 py-[5px] gap-x-2 rounded-full bg-carolina-blue shadow-sm no-underline text-white font-medium w-[45%] md:w-[146px]"
                      >
                        <PhoneIcon className="h-5 w-5" />
                        Call Now
                      </a>
                      <AskQuestionButton
                        listingDetails={data}
                        title="Inquire"
                        className="w-[45%] md:w-[146px] px-3 py-[5px] border !border-carolina-blue border-solid rounded-full font-medium text-black hover:text-black/70 !block text-sm md:text-base"
                      />
                    </div>
                  </div>
                  <div className="border border-none md:border-solid border-platinium md:px-2 py-3 lg:p-6 w-full flex items-center justify-between my-4 md:my-[30px] rounded-2xl text-metal-gray">
                    <div className="flex items-center md:justify-center px-4 md:px-0 gap-x-2 text-sm md:text-base">
                      <Bedroom className="w-5 h-5" color="#1184B7" />
                      <span className="hidden md:block">
                        {getStringSingularPlural('Bedroom', 'Bedrooms', data.totalBedrooms)}
                      </span>
                      <span className="md:hidden">{data.totalBedrooms} BR</span>
                    </div>
                    <Separator orientation="vertical" className="h-6 hidden md:block" />
                    <div className="flex items-center md:justify-center px-4 md:px-0 gap-x-2 text-sm md:text-base">
                      <SvgBathroom className="w-5 h-5" color="#1184B7" />
                      <span className="hidden md:block">
                        {getStringSingularPlural('Bathroom', 'Bathrooms', data.totalBathrooms)}
                      </span>
                      <span className="md:hidden">
                        {getStringSingularPlural('Bath', 'Baths', data.totalBathrooms)}
                      </span>
                    </div>
                    <Separator orientation="vertical" className="h-6 hidden md:block" />
                    <div className="flex items-center md:justify-center px-4 md:px-0 gap-x-2 text-sm md:text-base">
                      <SvgGuests className="w-5 h-5 text-foundation-blue" />
                      {getStringSingularPlural('Guest', 'Guests', data.totalCapacity)}
                    </div>
                    {data.totalLivingArea > 0 && (
                      <>
                        <Separator orientation="vertical" className="h-6 hidden md:block" />
                        <div className=" hidden md:flex items-center md:justify-center px-4 md:px-0 gap-x-2 text-sm md:text-base">
                          <SvgPatio className="w-5 h-5" color="#1184B7" />
                          {data.totalLivingArea} SF
                        </div>
                      </>
                    )}
                  </div>
                  {data?.highlights && (
                    <>
                      <div className="p-2.5 md:p-4 rounded-2xl border border-solid border-platinium mb-4 md:mb-[30px]">
                        <H2 className="m-0 mb-4 md:mb-6">What Guests Love</H2>
                        <ExpandableText
                          text={data?.highlights ?? ''}
                          className="text-sm text-metal-gray m-0"
                        />
                      </div>
                    </>
                  )}

                  <AllSleepingAreasTrigger
                    showButton={data.bedrooms.length > 4}
                    allSleepingAreasNode={data.bedrooms.map((_b, index) => (
                      <div
                        className="flex items-center justify-between px-4 md:px-6 py-4 rounded-2xl border border-solid border-platinium text-metal-gray gap-x-2 md:gap-x-0"
                        key={index}
                      >
                        <div className="flex text-sm md:text-base items-center gap-x-2 md:gap-x-4 w-4/12">
                          <Bedroom className="w-5 h-5" color="#1184B7" />
                          Bedroom {index + 1}
                        </div>
                        <p className="m-0 w-4/12 text-sm md:text-base">
                          {_b.nr_property_NrPropSleepArrange.map((_bed) => (
                            <span key={_bed.bedTypeId} className="mr-2">
                              {_bed.bedType} {_bed.totalBeds > 1 && `(${_bed.totalBeds})`}
                            </span>
                          ))}
                        </p>
                        <p className="m-0 md:text-right w-4/12 text-sm md:text-base">
                          {_b.floorNumber
                            ? isNaN(Number(_b.floorNumber))
                              ? _b.floorNumber
                              : `${getOrdinalRules(Number(_b.floorNumber))} floor`
                            : ''}
                        </p>
                      </div>
                    ))}
                  >
                    {data.bedrooms.slice(0, 4).map((_b, index) => (
                      <div
                        className="flex items-center justify-between px-4 md:px-6 py-4 rounded-2xl border border-solid border-platinium text-metal-gray gap-x-2 md:gap-x-0"
                        key={index}
                      >
                        <div className="flex text-sm md:text-base items-center gap-x-2 md:gap-x-4 w-4/12">
                          <Bedroom className="w-5 h-5" color="#1184B7" />
                          Bedroom {index + 1}
                        </div>
                        <p className="m-0 w-4/12 text-sm md:text-base">
                          {_b.nr_property_NrPropSleepArrange.map((_bed) => (
                            <span key={_bed.bedTypeId} className="mr-2">
                              {_bed.bedType} {_bed.totalBeds > 1 && `(${_bed.totalBeds})`}
                            </span>
                          ))}
                        </p>
                        <p className="m-0 md:text-right w-4/12 text-sm md:text-base">
                          {_b.floorNumber
                            ? isNaN(Number(_b.floorNumber))
                              ? _b.floorNumber
                              : `${getOrdinalRules(Number(_b.floorNumber))} floor`
                            : ''}
                        </p>
                      </div>
                    ))}
                  </AllSleepingAreasTrigger>
                  <Separator className="my-2 md:my-3 bg-transparent" />
                  <Amenities
                    essentials={data.essentials}
                    entertainment={data.entertainment}
                    outdoor={data.outdoorAmenities}
                    kitchenAmenities={data.kitchenAmenities}
                    featuredAmenities={data.featuredAmenities}
                  />
                  <Separator className="my-4 md:my-8" />
                  <ExpandableText
                    className="text-sm md:text-base text-metal-gray m-0"
                    text={data.description}
                  />
                  <Separator className="my-4 md:my-8" />
                  <H2 className="m-0 mb-4 md:mb-6 flex items-center gap-x-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="36"
                      height="37"
                      viewBox="0 0 36 37"
                      fill="none"
                    >
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M6.375 13.2207C6.375 7.12769 11.6572 2.32373 18 2.32373C24.3428 2.32373 29.625 7.12769 29.625 13.2207C29.625 18.9374 26.0807 25.6488 20.3622 28.0926C18.8609 28.7341 17.1391 28.7341 15.6378 28.0926C9.91933 25.6488 6.375 18.9374 6.375 13.2207ZM18 4.57373C12.7448 4.57373 8.625 8.51987 8.625 13.2207C8.625 18.2132 11.7831 23.9984 16.522 26.0236C17.4585 26.4238 18.5415 26.4238 19.478 26.0236C24.2169 23.9984 27.375 18.2132 27.375 13.2207C27.375 8.51987 23.2552 4.57373 18 4.57373ZM18 12.0737C16.9645 12.0737 16.125 12.9132 16.125 13.9487C16.125 14.9843 16.9645 15.8237 18 15.8237C19.0355 15.8237 19.875 14.9843 19.875 13.9487C19.875 12.9132 19.0355 12.0737 18 12.0737ZM13.875 13.9487C13.875 11.6706 15.7218 9.82373 18 9.82373C20.2782 9.82373 22.125 11.6706 22.125 13.9487C22.125 16.2269 20.2782 18.0737 18 18.0737C15.7218 18.0737 13.875 16.2269 13.875 13.9487ZM5.39312 22.9436C5.81016 23.4042 5.77488 24.1156 5.31432 24.5326C4.46278 25.3037 4.125 26.0418 4.125 26.6987C4.125 27.8442 5.21101 29.256 7.85506 30.4458C10.3935 31.5881 13.9793 32.3237 18 32.3237C22.0207 32.3237 25.6065 31.5881 28.1449 30.4458C30.789 29.256 31.875 27.8442 31.875 26.6987C31.875 26.0418 31.5372 25.3037 30.6857 24.5326C30.2251 24.1156 30.1898 23.4042 30.6069 22.9436C31.0239 22.4831 31.7354 22.4478 32.1959 22.8648C33.3404 23.9012 34.125 25.2004 34.125 26.6987C34.125 29.2811 31.8531 31.2444 29.0683 32.4976C26.1778 33.7983 22.2635 34.5737 18 34.5737C13.7365 34.5737 9.82223 33.7983 6.93174 32.4976C4.14685 31.2444 1.875 29.2811 1.875 26.6987C1.875 25.2004 2.65957 23.9012 3.80408 22.8648C4.26464 22.4478 4.97608 22.4831 5.39312 22.9436Z"
                        fill="#1184B7"
                      />
                    </svg>
                    Explore the Neighborhood
                  </H2>

                  <div className="rounded-lg overflow-hidden">
                    <GoogleMapsEmbed
                      apiKey={MAPS_API_KEY}
                      mode="place"
                      zoom="16"
                      height={340}
                      width="100%"
                      q={`${data.latitude ?? 41.2835},${data.longitude ?? -70.0995}`}
                    />
                  </div>
                  <div className="my-4 md:my-8 border border-solid border-platinium px-6 py-4 rounded-lg">
                    <div className="flex items-start justify-between my-2">
                      <p className="flex items-start gap-x-4 text-[#696F8C] max-w-[70%] text-sm md:text-base m-0">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 36 37"
                          fill="none"
                        >
                          <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M6.375 13.2207C6.375 7.12769 11.6572 2.32373 18 2.32373C24.3428 2.32373 29.625 7.12769 29.625 13.2207C29.625 18.9374 26.0807 25.6488 20.3622 28.0926C18.8609 28.7341 17.1391 28.7341 15.6378 28.0926C9.91933 25.6488 6.375 18.9374 6.375 13.2207ZM18 4.57373C12.7448 4.57373 8.625 8.51987 8.625 13.2207C8.625 18.2132 11.7831 23.9984 16.522 26.0236C17.4585 26.4238 18.5415 26.4238 19.478 26.0236C24.2169 23.9984 27.375 18.2132 27.375 13.2207C27.375 8.51987 23.2552 4.57373 18 4.57373ZM18 12.0737C16.9645 12.0737 16.125 12.9132 16.125 13.9487C16.125 14.9843 16.9645 15.8237 18 15.8237C19.0355 15.8237 19.875 14.9843 19.875 13.9487C19.875 12.9132 19.0355 12.0737 18 12.0737ZM13.875 13.9487C13.875 11.6706 15.7218 9.82373 18 9.82373C20.2782 9.82373 22.125 11.6706 22.125 13.9487C22.125 16.2269 20.2782 18.0737 18 18.0737C15.7218 18.0737 13.875 16.2269 13.875 13.9487ZM5.39312 22.9436C5.81016 23.4042 5.77488 24.1156 5.31432 24.5326C4.46278 25.3037 4.125 26.0418 4.125 26.6987C4.125 27.8442 5.21101 29.256 7.85506 30.4458C10.3935 31.5881 13.9793 32.3237 18 32.3237C22.0207 32.3237 25.6065 31.5881 28.1449 30.4458C30.789 29.256 31.875 27.8442 31.875 26.6987C31.875 26.0418 31.5372 25.3037 30.6857 24.5326C30.2251 24.1156 30.1898 23.4042 30.6069 22.9436C31.0239 22.4831 31.7354 22.4478 32.1959 22.8648C33.3404 23.9012 34.125 25.2004 34.125 26.6987C34.125 29.2811 31.8531 31.2444 29.0683 32.4976C26.1778 33.7983 22.2635 34.5737 18 34.5737C13.7365 34.5737 9.82223 33.7983 6.93174 32.4976C4.14685 31.2444 1.875 29.2811 1.875 26.6987C1.875 25.2004 2.65957 23.9012 3.80408 22.8648C4.26464 22.4478 4.97608 22.4831 5.39312 22.9436Z"
                            fill="#1184B7"
                          />
                        </svg>
                        Nearest Beach
                      </p>
                      <p className="text-foundation-blue text-sm md:text-base m-0">
                        {Number(data?.distToBeach ?? 0)} miles
                      </p>
                    </div>
                    <div className="flex items-start justify-between my-2">
                      <p className="flex items-start gap-x-4 text-[#696F8C] max-w-[70%] text-sm md:text-base m-0">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 36 37"
                          fill="none"
                        >
                          <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M6.375 13.2207C6.375 7.12769 11.6572 2.32373 18 2.32373C24.3428 2.32373 29.625 7.12769 29.625 13.2207C29.625 18.9374 26.0807 25.6488 20.3622 28.0926C18.8609 28.7341 17.1391 28.7341 15.6378 28.0926C9.91933 25.6488 6.375 18.9374 6.375 13.2207ZM18 4.57373C12.7448 4.57373 8.625 8.51987 8.625 13.2207C8.625 18.2132 11.7831 23.9984 16.522 26.0236C17.4585 26.4238 18.5415 26.4238 19.478 26.0236C24.2169 23.9984 27.375 18.2132 27.375 13.2207C27.375 8.51987 23.2552 4.57373 18 4.57373ZM18 12.0737C16.9645 12.0737 16.125 12.9132 16.125 13.9487C16.125 14.9843 16.9645 15.8237 18 15.8237C19.0355 15.8237 19.875 14.9843 19.875 13.9487C19.875 12.9132 19.0355 12.0737 18 12.0737ZM13.875 13.9487C13.875 11.6706 15.7218 9.82373 18 9.82373C20.2782 9.82373 22.125 11.6706 22.125 13.9487C22.125 16.2269 20.2782 18.0737 18 18.0737C15.7218 18.0737 13.875 16.2269 13.875 13.9487ZM5.39312 22.9436C5.81016 23.4042 5.77488 24.1156 5.31432 24.5326C4.46278 25.3037 4.125 26.0418 4.125 26.6987C4.125 27.8442 5.21101 29.256 7.85506 30.4458C10.3935 31.5881 13.9793 32.3237 18 32.3237C22.0207 32.3237 25.6065 31.5881 28.1449 30.4458C30.789 29.256 31.875 27.8442 31.875 26.6987C31.875 26.0418 31.5372 25.3037 30.6857 24.5326C30.2251 24.1156 30.1898 23.4042 30.6069 22.9436C31.0239 22.4831 31.7354 22.4478 32.1959 22.8648C33.3404 23.9012 34.125 25.2004 34.125 26.6987C34.125 29.2811 31.8531 31.2444 29.0683 32.4976C26.1778 33.7983 22.2635 34.5737 18 34.5737C13.7365 34.5737 9.82223 33.7983 6.93174 32.4976C4.14685 31.2444 1.875 29.2811 1.875 26.6987C1.875 25.2004 2.65957 23.9012 3.80408 22.8648C4.26464 22.4478 4.97608 22.4831 5.39312 22.9436Z"
                            fill="#1184B7"
                          />
                        </svg>
                        Distance to Hub
                      </p>
                      <p className="text-foundation-blue text-sm md:text-base m-0">
                        {Number(data?.distToTheHub ?? 0)} miles
                      </p>
                    </div>
                  </div>
                  <HouseRules data={data} />
                  <div className="my-4 p-2 flex items-center gap-x-2 md:hidden">
                    <SvgPropertyVerification />
                    <p className="text-metal-gray text-sm">Verified home, professionally managed</p>
                  </div>
                  <Separator className="my-4 md:my-8 md:hidden" />
                  <div className="flex md:hidden items-center justify-between gap-x-3">
                    <a
                      href="tel:************"
                      className="flex items-center justify-center py-4 gap-x-2 rounded-full bg-carolina-blue shadow-sm no-underline text-white font-medium w-[calc(50%-6px)] h-[54px]"
                    >
                      <PhoneIcon className="h-5 w-5" />
                      Call Now
                    </a>
                    <AskQuestionButton
                      listingDetails={data}
                      title="Inquire"
                      icon={<ChatBubbleOvalLeftIcon className="w-4 h-4" />}
                      className="rounded-full border-solid !border-carolina-blue text-black font-medium w-[calc(50%-6px)] flex items-center gap-x-2 py-4 h-[54px]"
                    />
                  </div>
                  <div className="md:hidden px-6 py-4 rounded-2xl bg-[#F0F9FF] mt-4 border border-solid border-[#BAE6FD]">
                    <p className="text-center text-xl font-medium m-0">
                      Prefer to talk?
                      <br className="md:hidden" /> Call a rental specialist:
                    </p>
                    <a
                      href="tel:************"
                      className="bg-white border border-[#7DD3FC] border-solid flex items-center gap-x-2 px-4 py-2 my-2 rounded-full w-max mx-auto no-underline text-[#1E293B]"
                    >
                      <PhoneIcon className="w-5 h-5" />
                      <span className="text-lg font-medium">************</span>
                    </a>
                    <p className="text-[#64748B] text-sm text-center m-0">
                      Hours: 8am &#45; 8pm ET, 7 days a week
                    </p>
                  </div>
                  <Separator className="my-4 md:my-8 md:hidden" />
                </div>
                <div className="hidden md:block w-[480px] relative">
                  <div className="rounded-2xl rounded-b-none md:rounded-b-2xl border border-solid border-platinium shadow-card-25 p-6 bg-white">
                    <RequestToBookForm
                      data={data}
                      petsAllowed={
                        !!data.houseRules.find((_h) => _h.name === 'Pets Considered')?.activeFlag
                      }
                    />

                    <div className="px-6 py-4 rounded-2xl bg-[#F0F9FF] mt-4 border border-solid border-[#BAE6FD]">
                      <p className="text-center text-lg font-medium m-0">
                        Prefer to talk? Call a rental specialist:
                      </p>
                      <a
                        href="tel:************"
                        className="bg-white border border-[#7DD3FC] border-solid flex items-center gap-x-2 px-4 py-2 my-2 rounded-full w-max mx-auto no-underline text-[#1E293B]"
                      >
                        <PhoneIcon className="w-5 h-5" />
                        <span className="text-lg font-medium">************</span>
                      </a>
                      <p className="text-[#64748B] text-sm text-center m-0">
                        Hours: 8am &#45; 8pm ET, 7 days a week
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="fixed md:hidden left-0 right-0 bottom-0 min-h-[80px] shadow-card-25 rounded-2xl rounded-b-none bg-white z-10 p-4 border border-solid border-[#E5E5E5]">
                <MobileCheckAvaialability data={data} />
              </div>
            </div>
          </main>
        </ListingDetailsWrapper>
      </BookingContextProvider>
    );
  } catch (error) {
    console.error('Listing details page error:', error);
    throw new Error('Something went wrong while loading the page.');
  }
}

export const revalidate = 3600;
