import { Suspense } from 'react';

import { PhoneIcon } from '@heroicons/react/24/outline';

import { HOME_ROUTE } from '@/constants/routes';

import classNames from 'classnames';
import Image from 'next/image';
import Link from 'next/link';

import HeaderLinks from '../components/common/HeaderLinks';
import UserMenuWrapper from '../components/common/UserMenuWrapper';

type Props = {
  isMapView?: boolean;
};

const PropertySearchHeader = ({ isMapView }: Props) => {
  return (
    <div
      className={classNames('h-[76px] lg:h-auto relative w-full', {
        'shadow-header bg-white': !isMapView,
        'bg-transparent !absolute': isMapView,
      })}
    >
      <div className="w-full px-4 sm:px-6 lg:px-8 2xl:px-20">
        <div className="flex items-center py-5 lg:justify-between static">
          <Link
            prefetch={true}
            href={HOME_ROUTE}
            className={classNames('cursor-pointer', {
              'hidden lg:block': isMapView,
            })}
          >
            <Image
              alt="NR logo"
              src={`/images/logo.svg`}
              width="0"
              height="0"
              sizes="100vw"
              className="h-[20px] lg:h-[40px] w-[80px] lg:w-auto"
              priority
            />
          </Link>
          <div className="hidden lg:block">
            <HeaderLinks />
          </div>
          <div className="hidden lg:block">
            <a
              href="tel:************"
              className="flex items-center justify-center px-3 py-[5px] border-solid border-[#BAE6FD] gap-x-2 rounded-full bg-white no-underline text-[#0369A1] h-[50px]"
            >
              <PhoneIcon className="w-5 h-5" />
              <span className="font-medium">Call ************</span>
            </a>
          </div>
          <Suspense>
            <UserMenuWrapper />
          </Suspense>
        </div>
      </div>
    </div>
  );
};

export default PropertySearchHeader;
