'use client';

import { createContext, useCallback, useContext, useState } from 'react';

import { GoogleOAuthProvider } from '@react-oauth/google';

import LoadingSpinner from '@/components/ui/loading-spinner';
import { BookingPaymentMethod } from '@/types/booking';
import { Nullable, ProgressStatus } from '@/types/common';
import { AuthenticationStep } from '@/types/login';
import { ProfileData } from '@/types/profile';

import dynamic from 'next/dynamic';

const GOOGLE_CLIENT_ID = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID ?? '';

if (!GOOGLE_CLIENT_ID) {
  console.warn('⚠️ Google Client ID is missing in environment variables.');
}

export enum BookingFlowStep {
  DETAILS = 'DETAILS',
  REGISTER = 'REGISTER',
  BILLING_DETAILS = 'BILLING_DETAILS',
}

const LoginDialogWrapper = dynamic(
  () => import('@/clients/components/common/AuthenticationDialog/LoginDialogWrapper'),
  {
    ssr: false,
    loading: () => (
      <div className="fixed bg-black/40 inset-0 flex items-center justify-center text-white">
        <LoadingSpinner className="w-10 h-10" />
      </div>
    ),
  },
);

type BookingFormContextType = {
  isInsuranceAdded: boolean;
  setIsInsuranceAdded: (status: boolean) => void;
  paymentMethod: Nullable<BookingPaymentMethod>;
  setPaymentMethod: (method: Nullable<BookingPaymentMethod>) => void;
  message: string;
  setMessage: (str: string) => void;
  errors: { [key: string]: Nullable<string> };
  setErrors: (err: any) => void;
  progressStatus: Nullable<ProgressStatus>;
  setProgressStatus: (_p: Nullable<ProgressStatus>) => void;
  showLoginDialog: boolean;
  setShowLoginDialog: (status: boolean) => void;
  authStep: AuthenticationStep;
  setAuthStep: (step: AuthenticationStep) => void;
  phone: Nullable<string>;
  setPhone: (phone: Nullable<string>) => void;
  userId: Nullable<number>;
  setUserId: (userId: Nullable<number>) => void;
  step: BookingFlowStep;
  setStep: (step: BookingFlowStep) => void;
};

const BookingFormContext = createContext<BookingFormContextType | undefined>(undefined);

export const BookingFormContextProvider = ({
  children,
  userData,
}: {
  children: React.ReactNode;
  userData?: ProfileData;
}) => {
  const [showLoginDialog, setShowLoginDialog] = useState<boolean>(false);
  const [errors, setErrors] = useState<{ [key: string]: Nullable<string> }>({});
  const [isInsuranceAdded, setIsInsuranceAdded] = useState<boolean>(false);
  const [message, setMessage] = useState<string>('');
  const [paymentMethod, setPaymentMethod] = useState<Nullable<BookingPaymentMethod>>(null);
  const [progressStatus, setProgressStatus] = useState<Nullable<ProgressStatus>>(null);
  const [authStep, setAuthStep] = useState<AuthenticationStep>(AuthenticationStep.PHONE_ENTRY);
  const [phone, setPhone] = useState<Nullable<string>>(null);
  const [userId, setUserId] = useState<Nullable<number>>(userData?.nrUserId ?? null);
  const [step, setStep] = useState<BookingFlowStep>(BookingFlowStep.DETAILS);

  const onToggleLoginDialog = useCallback(() => {
    setShowLoginDialog((prev) => !prev);
  }, []);

  return (
    <BookingFormContext.Provider
      value={{
        isInsuranceAdded,
        setIsInsuranceAdded,
        paymentMethod,
        setPaymentMethod,
        message,
        setMessage,
        errors,
        setErrors,
        progressStatus,
        setProgressStatus,
        showLoginDialog,
        setShowLoginDialog,
        authStep,
        setAuthStep,
        phone,
        setPhone,
        userId,
        setUserId,
        step,
        setStep,
      }}
    >
      {children}
      {showLoginDialog && (
        <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
          <LoginDialogWrapper
            open={showLoginDialog}
            onToggle={onToggleLoginDialog}
            defaultAuthStep={authStep}
            defaultPhone={phone}
            defaultUserid={userId}
          />
        </GoogleOAuthProvider>
      )}
    </BookingFormContext.Provider>
  );
};

export const useBookingForm = () => {
  const context = useContext(BookingFormContext);
  if (!context) {
    throw new Error('useBookingForm must be used within a BookingFormContextProvider');
  }
  return context;
};
